#!/usr/bin/env python3
"""
MCP Server for Document Processing Integration
Provides tools for document upload, processing, and management with Docling integration
"""

import asyncio
import json
import logging
import os
import tempfile
import base64
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("document-processing-mcp-server")

# Document Processing API Configuration
DOC_PROCESSING_HOST = os.getenv("DOC_PROCESSING_HOST", "localhost")
DOC_PROCESSING_PORT = os.getenv("DOC_PROCESSING_PORT", "8888")
DOC_PROCESSING_BASE_URL = f"http://{DOC_PROCESSING_HOST}:{DOC_PROCESSING_PORT}"

class DocumentProcessingMCPServer:
    def __init__(self):
        self.server = Server("document-processing")
        self.http_client = httpx.AsyncClient(timeout=120.0)  # Longer timeout for document processing
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available document processing tools"""
            return [
                Tool(
                    name="upload_document",
                    description="Upload and process a document using Docling",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "file_content": {
                                "type": "string",
                                "description": "Base64 encoded file content"
                            },
                            "filename": {
                                "type": "string",
                                "description": "Original filename with extension"
                            },
                            "document_type": {
                                "type": "string",
                                "enum": ["pdf", "docx", "txt", "html", "md"],
                                "description": "Document type for processing",
                                "default": "pdf"
                            },
                            "extract_tables": {
                                "type": "boolean",
                                "description": "Extract tables from document",
                                "default": True
                            },
                            "extract_images": {
                                "type": "boolean",
                                "description": "Extract images from document",
                                "default": False
                            },
                            "ocr_enabled": {
                                "type": "boolean",
                                "description": "Enable OCR for scanned documents",
                                "default": True
                            },
                            "language": {
                                "type": "string",
                                "description": "Document language for processing",
                                "default": "auto",
                                "enum": ["auto", "en", "vi", "fr", "de", "es"]
                            },
                            "metadata": {
                                "type": "object",
                                "description": "Additional metadata for the document",
                                "properties": {
                                    "title": {"type": "string"},
                                    "author": {"type": "string"},
                                    "category": {"type": "string"},
                                    "tags": {
                                        "type": "array",
                                        "items": {"type": "string"}
                                    }
                                }
                            }
                        },
                        "required": ["file_content", "filename"]
                    }
                ),
                Tool(
                    name="process_document_url",
                    description="Process a document from URL using Docling",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "URL of the document to process"
                            },
                            "document_type": {
                                "type": "string",
                                "enum": ["pdf", "docx", "txt", "html", "md"],
                                "description": "Expected document type",
                                "default": "pdf"
                            },
                            "extract_tables": {
                                "type": "boolean",
                                "description": "Extract tables from document",
                                "default": True
                            },
                            "extract_images": {
                                "type": "boolean",
                                "description": "Extract images from document",
                                "default": False
                            },
                            "ocr_enabled": {
                                "type": "boolean",
                                "description": "Enable OCR for scanned documents",
                                "default": True
                            },
                            "metadata": {
                                "type": "object",
                                "description": "Additional metadata for the document"
                            }
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="get_processing_status",
                    description="Get the status of a document processing job",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "job_id": {
                                "type": "string",
                                "description": "Processing job ID"
                            }
                        },
                        "required": ["job_id"]
                    }
                ),
                Tool(
                    name="get_processed_document",
                    description="Retrieve processed document content and metadata",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "document_id": {
                                "type": "string",
                                "description": "Processed document ID"
                            },
                            "include_raw_content": {
                                "type": "boolean",
                                "description": "Include raw extracted content",
                                "default": True
                            },
                            "include_structured_data": {
                                "type": "boolean",
                                "description": "Include structured data (tables, etc.)",
                                "default": True
                            },
                            "include_metadata": {
                                "type": "boolean",
                                "description": "Include document metadata",
                                "default": True
                            }
                        },
                        "required": ["document_id"]
                    }
                ),
                Tool(
                    name="list_processed_documents",
                    description="List all processed documents with filtering",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of documents to return",
                                "default": 10,
                                "minimum": 1,
                                "maximum": 100
                            },
                            "offset": {
                                "type": "integer",
                                "description": "Offset for pagination",
                                "default": 0,
                                "minimum": 0
                            },
                            "document_type": {
                                "type": "string",
                                "description": "Filter by document type"
                            },
                            "status": {
                                "type": "string",
                                "enum": ["processing", "completed", "failed"],
                                "description": "Filter by processing status"
                            },
                            "date_from": {
                                "type": "string",
                                "description": "Filter documents from date (YYYY-MM-DD)"
                            },
                            "date_to": {
                                "type": "string",
                                "description": "Filter documents to date (YYYY-MM-DD)"
                            }
                        }
                    }
                ),
                Tool(
                    name="extract_document_tables",
                    description="Extract tables from a processed document",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "document_id": {
                                "type": "string",
                                "description": "Processed document ID"
                            },
                            "table_format": {
                                "type": "string",
                                "enum": ["json", "csv", "html", "markdown"],
                                "description": "Output format for tables",
                                "default": "json"
                            },
                            "include_metadata": {
                                "type": "boolean",
                                "description": "Include table metadata (position, size, etc.)",
                                "default": True
                            }
                        },
                        "required": ["document_id"]
                    }
                ),
                Tool(
                    name="search_document_content",
                    description="Search within processed document content",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "document_ids": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific document IDs to search (optional)"
                            },
                            "document_type": {
                                "type": "string",
                                "description": "Filter by document type"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of results",
                                "default": 10,
                                "minimum": 1,
                                "maximum": 50
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="delete_processed_document",
                    description="Delete a processed document and its data",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "document_id": {
                                "type": "string",
                                "description": "Document ID to delete"
                            },
                            "delete_files": {
                                "type": "boolean",
                                "description": "Also delete associated files",
                                "default": True
                            }
                        },
                        "required": ["document_id"]
                    }
                ),
                Tool(
                    name="get_processing_stats",
                    description="Get document processing statistics",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "include_type_breakdown": {
                                "type": "boolean",
                                "description": "Include breakdown by document type",
                                "default": True
                            },
                            "include_status_breakdown": {
                                "type": "boolean",
                                "description": "Include breakdown by processing status",
                                "default": True
                            },
                            "date_range": {
                                "type": "object",
                                "properties": {
                                    "start": {"type": "string"},
                                    "end": {"type": "string"}
                                },
                                "description": "Date range for statistics (YYYY-MM-DD)"
                            }
                        }
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "upload_document":
                    return await self._upload_document(arguments)
                elif name == "process_document_url":
                    return await self._process_document_url(arguments)
                elif name == "get_processing_status":
                    return await self._get_processing_status(arguments)
                elif name == "get_processed_document":
                    return await self._get_processed_document(arguments)
                elif name == "list_processed_documents":
                    return await self._list_processed_documents(arguments)
                elif name == "extract_document_tables":
                    return await self._extract_document_tables(arguments)
                elif name == "search_document_content":
                    return await self._search_document_content(arguments)
                elif name == "delete_processed_document":
                    return await self._delete_processed_document(arguments)
                elif name == "get_processing_stats":
                    return await self._get_processing_stats(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _upload_document(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Upload and process a document"""
        try:
            # Decode base64 content
            file_content = base64.b64decode(arguments["file_content"])
            filename = arguments["filename"]
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Prepare multipart form data
                files = {"file": (filename, open(temp_file_path, "rb"), "application/octet-stream")}
                
                # Prepare form data
                form_data = {
                    "document_type": arguments.get("document_type", "pdf"),
                    "extract_tables": str(arguments.get("extract_tables", True)).lower(),
                    "extract_images": str(arguments.get("extract_images", False)).lower(),
                    "ocr_enabled": str(arguments.get("ocr_enabled", True)).lower(),
                    "language": arguments.get("language", "auto")
                }
                
                if arguments.get("metadata"):
                    form_data["metadata"] = json.dumps(arguments["metadata"])
                
                response = await self.http_client.post(
                    f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/upload",
                    files=files,
                    data=form_data
                )
                response.raise_for_status()
                result = response.json()
                
                return [TextContent(
                    type="text",
                    text=f"Document upload initiated:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
                )]
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error uploading document: {str(e)}")
            return [TextContent(type="text", text=f"Error uploading document: {str(e)}")]

    async def _process_document_url(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Process document from URL"""
        try:
            payload = {
                "url": arguments["url"],
                "document_type": arguments.get("document_type", "pdf"),
                "extract_tables": arguments.get("extract_tables", True),
                "extract_images": arguments.get("extract_images", False),
                "ocr_enabled": arguments.get("ocr_enabled", True),
                "metadata": arguments.get("metadata", {})
            }
            
            response = await self.http_client.post(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/process-url",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Document processing from URL initiated:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error processing document from URL: {str(e)}")
            return [TextContent(type="text", text=f"Error processing document from URL: {str(e)}")]

    async def _get_processing_status(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get processing status"""
        try:
            job_id = arguments["job_id"]
            
            response = await self.http_client.get(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/status/{job_id}"
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Processing Status:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting processing status: {str(e)}")
            return [TextContent(type="text", text=f"Error getting processing status: {str(e)}")]

    async def _get_processed_document(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get processed document"""
        try:
            document_id = arguments["document_id"]
            
            params = {
                "include_raw_content": arguments.get("include_raw_content", True),
                "include_structured_data": arguments.get("include_structured_data", True),
                "include_metadata": arguments.get("include_metadata", True)
            }
            
            response = await self.http_client.get(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/{document_id}",
                params=params
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Processed Document:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting processed document: {str(e)}")
            return [TextContent(type="text", text=f"Error getting processed document: {str(e)}")]

    async def _list_processed_documents(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """List processed documents"""
        try:
            params = {
                "limit": arguments.get("limit", 10),
                "offset": arguments.get("offset", 0)
            }
            
            # Add optional filters
            for key in ["document_type", "status", "date_from", "date_to"]:
                if arguments.get(key):
                    params[key] = arguments[key]
            
            response = await self.http_client.get(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents",
                params=params
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Processed Documents List:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error listing processed documents: {str(e)}")
            return [TextContent(type="text", text=f"Error listing processed documents: {str(e)}")]

    async def _extract_document_tables(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Extract tables from document"""
        try:
            document_id = arguments["document_id"]
            
            params = {
                "table_format": arguments.get("table_format", "json"),
                "include_metadata": arguments.get("include_metadata", True)
            }
            
            response = await self.http_client.get(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/{document_id}/tables",
                params=params
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Extracted Tables:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
            return [TextContent(type="text", text=f"Error extracting tables: {str(e)}")]

    async def _search_document_content(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Search document content"""
        try:
            payload = {
                "query": arguments["query"],
                "limit": arguments.get("limit", 10)
            }
            
            if arguments.get("document_ids"):
                payload["document_ids"] = arguments["document_ids"]
            if arguments.get("document_type"):
                payload["document_type"] = arguments["document_type"]
            
            response = await self.http_client.post(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/search",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Search Results:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error searching document content: {str(e)}")
            return [TextContent(type="text", text=f"Error searching document content: {str(e)}")]

    async def _delete_processed_document(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Delete processed document"""
        try:
            document_id = arguments["document_id"]
            delete_files = arguments.get("delete_files", True)
            
            params = {"delete_files": delete_files}
            
            response = await self.http_client.delete(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/{document_id}",
                params=params
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Document deleted:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            return [TextContent(type="text", text=f"Error deleting document: {str(e)}")]

    async def _get_processing_stats(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get processing statistics"""
        try:
            params = {
                "include_type_breakdown": arguments.get("include_type_breakdown", True),
                "include_status_breakdown": arguments.get("include_status_breakdown", True)
            }
            
            if arguments.get("date_range"):
                params.update(arguments["date_range"])
            
            response = await self.http_client.get(
                f"{DOC_PROCESSING_BASE_URL}/api/v1/documents/stats",
                params=params
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Processing Statistics:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting processing stats: {str(e)}")
            return [TextContent(type="text", text=f"Error getting processing stats: {str(e)}")]

    async def run(self):
        """Run the MCP server"""
        from mcp.types import ServerCapabilities
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="document-processing",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = DocumentProcessingMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())