#!/usr/bin/env python3
"""
MCP Server for Mem0 Memory System Integration
Provides tools to interact with the AccA Mem0 memory system
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel,
    ServerCapabilities
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mem0-mcp-server")

# AccA Backend API Configuration
BACKEND_BASE_URL = "http://localhost:8011"
MEMORY_API_BASE = f"{BACKEND_BASE_URL}/api/v1/memory"

class Mem0MCPServer:
    def __init__(self):
        self.server = Server("mem0-system")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available Mem0 tools"""
            return [
                Tool(
                    name="mem0_add_memory",
                    description="Add new memory from conversation messages with enhanced metadata",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "messages": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "role": {"type": "string", "enum": ["user", "assistant", "system"]},
                                        "content": {"type": "string"}
                                    },
                                    "required": ["role", "content"]
                                },
                                "description": "List of conversation messages to store as memory"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID for memory storage",
                                "default": "default_user"
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Optional session ID for grouping memories"
                            },
                            "conversation_id": {
                                "type": "string", 
                                "description": "Optional conversation ID for context"
                            },
                            "metadata": {
                                "type": "object",
                                "description": "Additional metadata to attach to memory"
                            }
                        },
                        "required": ["messages"]
                    }
                ),
                Tool(
                    name="mem0_search_memories",
                    description="Search for relevant memories with metadata filtering",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query to find relevant memories"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID to search memories for",
                                "default": "default_user"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of memories to return",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 20
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Filter by session ID"
                            },
                            "source": {
                                "type": "string",
                                "description": "Filter by source (user, assistant, system)"
                            },
                            "tags": {
                                "type": "string",
                                "description": "Filter by tags (comma-separated)"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="mem0_enhance_prompt",
                    description="Enhance conversation with relevant memories using metadata",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "messages": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "role": {"type": "string", "enum": ["user", "assistant", "system"]},
                                        "content": {"type": "string"}
                                    },
                                    "required": ["role", "content"]
                                },
                                "description": "Conversation messages to enhance"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID for memory retrieval",
                                "default": "default_user"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "Maximum number of memories to use for enhancement",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 10
                            },
                            "session_id": {
                                "type": "string",
                                "description": "Session ID for context-aware enhancement"
                            }
                        },
                        "required": ["messages"]
                    }
                ),
                Tool(
                    name="mem0_get_all_memories",
                    description="Get all memories for a specific user",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "User ID to get memories for",
                                "default": "default_user"
                            }
                        }
                    }
                ),
                Tool(
                    name="mem0_add_feedback",
                    description="Add user feedback to a memory for learning purposes",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "memory_id": {
                                "type": "string",
                                "description": "ID of the memory to provide feedback for"
                            },
                            "feedback": {
                                "type": "string",
                                "enum": ["positive", "negative", "neutral"],
                                "description": "Type of feedback"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID providing the feedback",
                                "default": "default_user"
                            }
                        },
                        "required": ["memory_id", "feedback"]
                    }
                ),
                Tool(
                    name="mem0_get_analytics",
                    description="Get analytics about user's memory usage",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "User ID to get analytics for",
                                "default": "default_user"
                            }
                        }
                    }
                ),
                Tool(
                    name="mem0_delete_memory",
                    description="Delete a specific memory",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "memory_id": {
                                "type": "string",
                                "description": "ID of the memory to delete"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "User ID who owns the memory",
                                "default": "default_user"
                            }
                        },
                        "required": ["memory_id"]
                    }
                ),
                Tool(
                    name="mem0_get_settings",
                    description="Get current Mem0 system settings",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="mem0_update_settings",
                    description="Update Mem0 system settings",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "settings": {
                                "type": "object",
                                "description": "Settings object to update",
                                "properties": {
                                    "enabled": {"type": "boolean"},
                                    "enable_metadata": {"type": "boolean"},
                                    "auto_keyword_extraction": {"type": "boolean"},
                                    "auto_tag_extraction": {"type": "boolean"},
                                    "track_user_feedback": {"type": "boolean"},
                                    "embedding_provider": {
                                        "type": "object",
                                        "properties": {
                                            "provider": {"type": "string"},
                                            "model": {"type": "string"},
                                            "api_key": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "required": ["settings"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "mem0_add_memory":
                    return await self._add_memory(arguments)
                elif name == "mem0_search_memories":
                    return await self._search_memories(arguments)
                elif name == "mem0_enhance_prompt":
                    return await self._enhance_prompt(arguments)
                elif name == "mem0_get_all_memories":
                    return await self._get_all_memories(arguments)
                elif name == "mem0_add_feedback":
                    return await self._add_feedback(arguments)
                elif name == "mem0_get_analytics":
                    return await self._get_analytics(arguments)
                elif name == "mem0_delete_memory":
                    return await self._delete_memory(arguments)
                elif name == "mem0_get_settings":
                    return await self._get_settings(arguments)
                elif name == "mem0_update_settings":
                    return await self._update_settings(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _add_memory(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Add memory via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            payload = {
                "messages": arguments["messages"],
                "session_id": arguments.get("session_id"),
                "conversation_id": arguments.get("conversation_id"),
                "metadata": arguments.get("metadata")
            }
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/add",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Memory added successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error adding memory: {str(e)}")
            return [TextContent(type="text", text=f"Error adding memory: {str(e)}")]

    async def _search_memories(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Search memories via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            params = {
                "query": arguments["query"],
                "limit": arguments.get("limit", 3)
            }
            
            # Add optional filters
            if arguments.get("session_id"):
                params["session_id"] = arguments["session_id"]
            if arguments.get("source"):
                params["source"] = arguments["source"]
            if arguments.get("tags"):
                params["tags"] = arguments["tags"]
            
            response = await self.http_client.get(
                f"{MEMORY_API_BASE}/memories/search",
                params=params,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            memories = result.get("results", [])
            if not memories:
                return [TextContent(type="text", text="No relevant memories found.")]
            
            memory_text = "Found relevant memories:\n\n"
            for i, memory in enumerate(memories, 1):
                memory_content = memory.get("memory", "")
                score = memory.get("score", 0)
                analysis = memory.get("analysis", {})
                
                memory_text += f"{i}. Memory (Score: {score:.3f}):\n{memory_content}\n"
                
                if analysis.get("keywords"):
                    memory_text += f"   Keywords: {', '.join(analysis['keywords'][:5])}\n"
                if analysis.get("language"):
                    memory_text += f"   Language: {analysis['language']}\n"
                if analysis.get("estimated_importance", 1) > 2:
                    memory_text += f"   Importance: High\n"
                memory_text += "\n"
            
            return [TextContent(type="text", text=memory_text)]
        except Exception as e:
            logger.error(f"Error searching memories: {str(e)}")
            return [TextContent(type="text", text=f"Error searching memories: {str(e)}")]

    async def _enhance_prompt(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Enhance prompt with memories via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            params = {
                "limit": arguments.get("limit", 3)
            }
            if arguments.get("session_id"):
                params["session_id"] = arguments["session_id"]
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/enhance",
                json=arguments["messages"],
                params=params,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            enhanced_messages = result.get("enhanced_messages", arguments["messages"])
            
            return [TextContent(
                type="text",
                text=f"Enhanced conversation with {len(enhanced_messages)} messages:\n\n" +
                     json.dumps(enhanced_messages, indent=2, ensure_ascii=False)
            )]
        except Exception as e:
            logger.error(f"Error enhancing prompt: {str(e)}")
            return [TextContent(type="text", text=f"Error enhancing prompt: {str(e)}")]

    async def _get_all_memories(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get all memories via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            response = await self.http_client.get(
                f"{MEMORY_API_BASE}/memories",
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            memories = result.get("memories", [])
            if not memories:
                return [TextContent(type="text", text="No memories found for this user.")]
            
            memory_text = f"Found {len(memories)} memories:\n\n"
            for i, memory in enumerate(memories, 1):
                memory_content = memory.get("memory", "")
                memory_id = memory.get("id", "unknown")
                memory_text += f"{i}. ID: {memory_id}\n{memory_content}\n\n"
            
            return [TextContent(type="text", text=memory_text)]
        except Exception as e:
            logger.error(f"Error getting all memories: {str(e)}")
            return [TextContent(type="text", text=f"Error getting all memories: {str(e)}")]

    async def _add_feedback(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Add feedback via API"""
        try:
            headers = {"Content-Type": "application/json"}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            payload = {
                "memory_id": arguments["memory_id"],
                "feedback": arguments["feedback"]
            }
            
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/memories/feedback",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Feedback added successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error adding feedback: {str(e)}")
            return [TextContent(type="text", text=f"Error adding feedback: {str(e)}")]

    async def _get_analytics(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get analytics via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            response = await self.http_client.get(
                f"{MEMORY_API_BASE}/memories/analytics",
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            analytics_text = "Memory Analytics:\n\n"
            analytics_text += f"Total Memories: {result.get('total_memories', 0)}\n"
            
            if result.get('languages'):
                analytics_text += f"Languages: {json.dumps(result['languages'], indent=2)}\n"
            
            if result.get('sources'):
                analytics_text += f"Sources: {json.dumps(result['sources'], indent=2)}\n"
            
            if result.get('generated_at'):
                analytics_text += f"Generated at: {result['generated_at']}\n"
            
            return [TextContent(type="text", text=analytics_text)]
        except Exception as e:
            logger.error(f"Error getting analytics: {str(e)}")
            return [TextContent(type="text", text=f"Error getting analytics: {str(e)}")]

    async def _delete_memory(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Delete memory via API"""
        try:
            headers = {}
            if arguments.get("user_id"):
                headers["X-User-ID"] = arguments["user_id"]
            
            response = await self.http_client.delete(
                f"{MEMORY_API_BASE}/memories/{arguments['memory_id']}",
                headers=headers
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Memory deleted successfully: {result.get('message', 'Success')}"
            )]
        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}")
            return [TextContent(type="text", text=f"Error deleting memory: {str(e)}")]

    async def _get_settings(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get settings via API"""
        try:
            response = await self.http_client.get(f"{MEMORY_API_BASE}/settings")
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Current Mem0 Settings:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
        except Exception as e:
            logger.error(f"Error getting settings: {str(e)}")
            return [TextContent(type="text", text=f"Error getting settings: {str(e)}")]

    async def _update_settings(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Update settings via API"""
        try:
            response = await self.http_client.post(
                f"{MEMORY_API_BASE}/settings",
                json=arguments["settings"],
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Settings updated successfully:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
        except Exception as e:
            logger.error(f"Error updating settings: {str(e)}")
            return [TextContent(type="text", text=f"Error updating settings: {str(e)}")]

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="mem0-system",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = Mem0MCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())