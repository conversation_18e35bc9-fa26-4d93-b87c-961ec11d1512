#!/usr/bin/env python3
"""
HTTP Wrapper for Gemini CLI Tools MCP Server
"""

import async<PERSON>
import json
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from server import GeminiCLIServer

app = FastAPI(
    title="Gemini CLI Tools",
    description="HTTP wrapper for Gemini CLI Tools MCP Server",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Global MCP server instance
mcp_server = None

@app.on_event("startup")
async def startup_event():
    """Initialize MCP server on startup"""
    global mcp_server
    mcp_server = GeminiCLIServer()
    print("🚀 Gemini CLI Tools HTTP server started")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Gemini CLI Tools HTTP Server", "tools": 11}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "gemini-cli-tools"}

@app.get("/gemini_cli_tools/openapi.json")
async def get_openapi():
    """Return OpenAPI spec for Open WebUI integration"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Gemini CLI Tools",
            "description": "Comprehensive tool collection from Gemini CLI Wrapper",
            "version": "1.0.0"
        },
        "paths": {
            "/tools/web_search": {
                "post": {
                    "summary": "Search the web using Gemini CLI",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "max_results": {"type": "number", "default": 5}
                        },
                        "required": ["query"]
                    }
                }
            },
            "/tools/read_file": {
                "post": {
                    "summary": "Read and analyze a file",
                    "parameters": {
                        "type": "object", 
                        "properties": {
                            "path": {"type": "string", "description": "File path"},
                            "encoding": {"type": "string", "default": "utf-8"}
                        },
                        "required": ["path"]
                    }
                }
            },
            "/tools/write_file": {
                "post": {
                    "summary": "Write content to a file",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string", "description": "File path"},
                            "content": {"type": "string", "description": "File content"}
                        },
                        "required": ["path", "content"]
                    }
                }
            },
            "/tools/execute_command": {
                "post": {
                    "summary": "Execute shell command (restricted)",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "command": {"type": "string", "description": "Command to execute"},
                            "timeout": {"type": "number", "default": 30}
                        },
                        "required": ["command"]
                    }
                }
            },
            "/tools/list_directory": {
                "post": {
                    "summary": "List directory contents",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string", "default": "."},
                            "ignore_patterns": {"type": "array", "items": {"type": "string"}}
                        },
                        "required": ["path"]
                    }
                }
            },
            "/tools/search_file_content": {
                "post": {
                    "summary": "Search file content with regex",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "pattern": {"type": "string", "description": "Regex pattern"},
                            "directory": {"type": "string", "default": "."},
                            "file_pattern": {"type": "string", "default": "*"},
                            "recursive": {"type": "boolean", "default": True}
                        },
                        "required": ["pattern"]
                    }
                }
            },
            "/tools/glob": {
                "post": {
                    "summary": "Find files matching glob patterns",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "pattern": {"type": "string", "description": "Glob pattern"},
                            "base_directory": {"type": "string", "default": "."},
                            "sort_by_time": {"type": "boolean", "default": True}
                        },
                        "required": ["pattern"]
                    }
                }
            },
            "/tools/web_fetch": {
                "post": {
                    "summary": "Fetch content from URLs",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "urls": {"type": "array", "items": {"type": "string"}, "maxItems": 20},
                            "instructions": {"type": "string"},
                            "timeout": {"type": "number", "default": 10}
                        },
                        "required": ["urls"]
                    }
                }
            },
            "/tools/read_many_files": {
                "post": {
                    "summary": "Read multiple files",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "paths": {"type": "array", "items": {"type": "string"}},
                            "target_directory": {"type": "string", "default": "."},
                            "include_metadata": {"type": "boolean", "default": True}
                        },
                        "required": ["paths"]
                    }
                }
            },
            "/tools/analyze_code": {
                "post": {
                    "summary": "Analyze code structure",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "path": {"type": "string", "description": "Path to analyze"},
                            "analysis_type": {"type": "string", "enum": ["structure", "dependencies"], "default": "structure"}
                        },
                        "required": ["path"]
                    }
                }
            },
            "/tools/store_memory": {
                "post": {
                    "summary": "Store memory for conversation context",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "key": {"type": "string", "description": "Memory key"},
                            "value": {"type": "string", "description": "Value to store"},
                            "ttl": {"type": "number", "default": 3600}
                        },
                        "required": ["key", "value"]
                    }
                }
            }
        },
        "specs": [
            {"type": "function", "name": "tool_web_search_post", "description": "Search the web using Gemini CLI"},
            {"type": "function", "name": "tool_read_file_post", "description": "Read and analyze a file"},
            {"type": "function", "name": "tool_write_file_post", "description": "Write content to a file"},
            {"type": "function", "name": "tool_execute_command_post", "description": "Execute shell command (restricted)"},
            {"type": "function", "name": "tool_list_directory_post", "description": "List directory contents"},
            {"type": "function", "name": "tool_search_file_content_post", "description": "Search file content with regex"},
            {"type": "function", "name": "tool_glob_post", "description": "Find files matching glob patterns"},
            {"type": "function", "name": "tool_web_fetch_post", "description": "Fetch content from URLs"},
            {"type": "function", "name": "tool_read_many_files_post", "description": "Read multiple files"},
            {"type": "function", "name": "tool_analyze_code_post", "description": "Analyze code structure"},
            {"type": "function", "name": "tool_store_memory_post", "description": "Store memory for conversation context"}
        ]
    }

@app.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, payload: Dict[str, Any]):
    """Execute a specific tool"""
    global mcp_server
    
    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")
    
    try:
        # Route to appropriate tool handler
        if tool_name == "web_search":
            result = await mcp_server._web_search(payload)
        elif tool_name == "read_file":
            result = await mcp_server._read_file(payload)
        elif tool_name == "write_file":
            result = await mcp_server._write_file(payload)
        elif tool_name == "execute_command":
            result = await mcp_server._execute_command(payload)
        elif tool_name == "analyze_code":
            result = await mcp_server._analyze_code(payload)
        elif tool_name == "store_memory":
            result = await mcp_server._store_memory(payload)
        elif tool_name == "list_directory":
            result = await mcp_server._list_directory(payload)
        elif tool_name == "search_file_content":
            result = await mcp_server._search_file_content(payload)
        elif tool_name == "glob":
            result = await mcp_server._glob(payload)
        elif tool_name == "web_fetch":
            result = await mcp_server._web_fetch(payload)
        elif tool_name == "read_many_files":
            result = await mcp_server._read_many_files(payload)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        return JSONResponse(content=result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.post("/tools/call")
async def call_tool(payload: Dict[str, Any]):
    """Call a tool with name and arguments"""
    global mcp_server
    
    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")
    
    tool_name = payload.get("name")
    arguments = payload.get("arguments", {})
    
    if not tool_name:
        raise HTTPException(status_code=400, detail="Tool name is required")
    
    try:
        # Route to appropriate tool handler
        if tool_name == "web_search":
            result = await mcp_server._web_search(arguments)
        elif tool_name == "read_file":
            result = await mcp_server._read_file(arguments)
        elif tool_name == "write_file":
            result = await mcp_server._write_file(arguments)
        elif tool_name == "execute_command":
            result = await mcp_server._execute_command(arguments)
        elif tool_name == "analyze_code":
            result = await mcp_server._analyze_code(arguments)
        elif tool_name == "store_memory":
            result = await mcp_server._store_memory(arguments)
        elif tool_name == "list_directory" or tool_name == "list_files":
            result = await mcp_server._list_directory(arguments)
        elif tool_name == "search_file_content":
            result = await mcp_server._search_file_content(arguments)
        elif tool_name == "glob":
            result = await mcp_server._glob(arguments)
        elif tool_name == "web_fetch":
            result = await mcp_server._web_fetch(arguments)
        elif tool_name == "read_many_files":
            result = await mcp_server._read_many_files(arguments)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        return JSONResponse(content={"result": result})
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.post("/tools/list")
async def list_tools():
    """List available tools"""
    return JSONResponse(content={
        "tools": [
            {"name": "web_search", "description": "Search the web using Gemini CLI"},
            {"name": "read_file", "description": "Read and analyze a file"},
            {"name": "write_file", "description": "Write content to a file"},
            {"name": "execute_command", "description": "Execute shell command (restricted)"},
            {"name": "list_directory", "description": "List directory contents"},
            {"name": "list_files", "description": "List directory contents (alias)"},
            {"name": "search_file_content", "description": "Search file content with regex"},
            {"name": "glob", "description": "Find files matching glob patterns"},
            {"name": "web_fetch", "description": "Fetch content from URLs"},
            {"name": "read_many_files", "description": "Read multiple files"},
            {"name": "analyze_code", "description": "Analyze code structure"},
            {"name": "store_memory", "description": "Store memory for conversation context"}
        ]
    })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8001))
    uvicorn.run(
        "http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
