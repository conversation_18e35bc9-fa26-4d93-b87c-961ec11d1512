#!/usr/bin/env python3
"""
MCP Server for Vietnamese Language Processing
Provides tools for Vietnamese text processing, translation, and language-specific operations
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel,
    ServerCapabilities
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("vietnamese-language-mcp-server")

class VietnameseLanguageMCPServer:
    def __init__(self):
        self.server = Server("vietnamese-language")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Vietnamese language patterns and utilities
        self.vietnamese_chars = "àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ"
        self.vietnamese_chars += self.vietnamese_chars.upper()
        
        # Common Vietnamese words for language detection
        self.vietnamese_common_words = {
            "và", "của", "có", "là", "trong", "với", "được", "cho", "từ", "về", 
            "một", "này", "đã", "sẽ", "các", "những", "người", "thời", "năm", "ngày",
            "việc", "làm", "tại", "để", "khi", "như", "sau", "trước", "theo", "đến"
        }
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available Vietnamese language tools"""
            return [
                Tool(
                    name="detect_vietnamese",
                    description="Detect if text contains Vietnamese language",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Text to analyze for Vietnamese language"
                            },
                            "threshold": {
                                "type": "number",
                                "description": "Confidence threshold (0.0-1.0)",
                                "default": 0.5,
                                "minimum": 0.0,
                                "maximum": 1.0
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="normalize_vietnamese_text",
                    description="Normalize Vietnamese text (remove diacritics, standardize)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to normalize"
                            },
                            "remove_diacritics": {
                                "type": "boolean",
                                "description": "Remove Vietnamese diacritics",
                                "default": False
                            },
                            "standardize_spacing": {
                                "type": "boolean",
                                "description": "Standardize whitespace and punctuation",
                                "default": True
                            },
                            "lowercase": {
                                "type": "boolean",
                                "description": "Convert to lowercase",
                                "default": False
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="extract_vietnamese_keywords",
                    description="Extract keywords from Vietnamese text",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to extract keywords from"
                            },
                            "max_keywords": {
                                "type": "integer",
                                "description": "Maximum number of keywords to extract",
                                "default": 10,
                                "minimum": 1,
                                "maximum": 50
                            },
                            "min_word_length": {
                                "type": "integer",
                                "description": "Minimum word length for keywords",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 10
                            },
                            "exclude_common_words": {
                                "type": "boolean",
                                "description": "Exclude common Vietnamese stop words",
                                "default": True
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="segment_vietnamese_text",
                    description="Segment Vietnamese text into sentences and words",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to segment"
                            },
                            "segment_type": {
                                "type": "string",
                                "enum": ["sentences", "words", "both"],
                                "description": "Type of segmentation to perform",
                                "default": "sentences"
                            },
                            "preserve_punctuation": {
                                "type": "boolean",
                                "description": "Preserve punctuation in segmentation",
                                "default": True
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="translate_vietnamese",
                    description="Translate text between Vietnamese and other languages",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Text to translate"
                            },
                            "source_language": {
                                "type": "string",
                                "enum": ["vi", "en", "auto"],
                                "description": "Source language code",
                                "default": "auto"
                            },
                            "target_language": {
                                "type": "string",
                                "enum": ["vi", "en"],
                                "description": "Target language code",
                                "default": "en"
                            },
                            "translation_service": {
                                "type": "string",
                                "enum": ["google", "libre", "local"],
                                "description": "Translation service to use",
                                "default": "libre"
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="analyze_vietnamese_sentiment",
                    description="Analyze sentiment of Vietnamese text",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to analyze sentiment"
                            },
                            "detailed_analysis": {
                                "type": "boolean",
                                "description": "Provide detailed sentiment breakdown",
                                "default": False
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="validate_vietnamese_text",
                    description="Validate Vietnamese text for common issues",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to validate"
                            },
                            "check_encoding": {
                                "type": "boolean",
                                "description": "Check for encoding issues",
                                "default": True
                            },
                            "check_diacritics": {
                                "type": "boolean",
                                "description": "Check for missing or incorrect diacritics",
                                "default": True
                            },
                            "check_grammar": {
                                "type": "boolean",
                                "description": "Basic grammar validation",
                                "default": False
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="convert_vietnamese_encoding",
                    description="Convert Vietnamese text between different encodings",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to convert"
                            },
                            "source_encoding": {
                                "type": "string",
                                "enum": ["utf-8", "tcvn3", "vni", "viscii", "vps"],
                                "description": "Source encoding",
                                "default": "utf-8"
                            },
                            "target_encoding": {
                                "type": "string",
                                "enum": ["utf-8", "tcvn3", "vni", "viscii", "vps"],
                                "description": "Target encoding",
                                "default": "utf-8"
                            }
                        },
                        "required": ["text"]
                    }
                ),
                Tool(
                    name="generate_vietnamese_summary",
                    description="Generate summary of Vietnamese text",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Vietnamese text to summarize"
                            },
                            "summary_length": {
                                "type": "string",
                                "enum": ["short", "medium", "long"],
                                "description": "Desired summary length",
                                "default": "medium"
                            },
                            "summary_type": {
                                "type": "string",
                                "enum": ["extractive", "abstractive"],
                                "description": "Type of summary to generate",
                                "default": "extractive"
                            }
                        },
                        "required": ["text"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "detect_vietnamese":
                    return await self._detect_vietnamese(arguments)
                elif name == "normalize_vietnamese_text":
                    return await self._normalize_vietnamese_text(arguments)
                elif name == "extract_vietnamese_keywords":
                    return await self._extract_vietnamese_keywords(arguments)
                elif name == "segment_vietnamese_text":
                    return await self._segment_vietnamese_text(arguments)
                elif name == "translate_vietnamese":
                    return await self._translate_vietnamese(arguments)
                elif name == "analyze_vietnamese_sentiment":
                    return await self._analyze_vietnamese_sentiment(arguments)
                elif name == "validate_vietnamese_text":
                    return await self._validate_vietnamese_text(arguments)
                elif name == "convert_vietnamese_encoding":
                    return await self._convert_vietnamese_encoding(arguments)
                elif name == "generate_vietnamese_summary":
                    return await self._generate_vietnamese_summary(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _detect_vietnamese(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Detect Vietnamese language in text"""
        try:
            text = arguments["text"]
            threshold = arguments.get("threshold", 0.5)
            
            # Count Vietnamese characters
            vietnamese_char_count = sum(1 for char in text if char in self.vietnamese_chars)
            total_chars = len([char for char in text if char.isalpha()])
            
            if total_chars == 0:
                vietnamese_char_ratio = 0.0
            else:
                vietnamese_char_ratio = vietnamese_char_count / total_chars
            
            # Count Vietnamese common words
            words = re.findall(r'\b\w+\b', text.lower())
            vietnamese_word_count = sum(1 for word in words if word in self.vietnamese_common_words)
            total_words = len(words)
            
            if total_words == 0:
                vietnamese_word_ratio = 0.0
            else:
                vietnamese_word_ratio = vietnamese_word_count / total_words
            
            # Combined confidence score
            confidence = (vietnamese_char_ratio * 0.6) + (vietnamese_word_ratio * 0.4)
            is_vietnamese = confidence >= threshold
            
            result = {
                "is_vietnamese": is_vietnamese,
                "confidence": round(confidence, 3),
                "threshold": threshold,
                "analysis": {
                    "vietnamese_characters": vietnamese_char_count,
                    "total_characters": total_chars,
                    "character_ratio": round(vietnamese_char_ratio, 3),
                    "vietnamese_words": vietnamese_word_count,
                    "total_words": total_words,
                    "word_ratio": round(vietnamese_word_ratio, 3)
                }
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Language Detection:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error detecting Vietnamese: {str(e)}")
            return [TextContent(type="text", text=f"Error detecting Vietnamese: {str(e)}")]

    async def _normalize_vietnamese_text(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Normalize Vietnamese text"""
        try:
            text = arguments["text"]
            remove_diacritics = arguments.get("remove_diacritics", False)
            standardize_spacing = arguments.get("standardize_spacing", True)
            lowercase = arguments.get("lowercase", False)
            
            normalized_text = text
            
            if remove_diacritics:
                # Simple diacritic removal mapping
                diacritic_map = {
                    'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
                    'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
                    'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
                    'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
                    'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
                    'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
                    'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
                    'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
                    'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
                    'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
                    'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
                    'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
                    'đ': 'd'
                }
                
                # Add uppercase mappings
                for key, value in list(diacritic_map.items()):
                    diacritic_map[key.upper()] = value.upper()
                
                for vietnamese_char, latin_char in diacritic_map.items():
                    normalized_text = normalized_text.replace(vietnamese_char, latin_char)
            
            if standardize_spacing:
                # Remove extra whitespace
                normalized_text = re.sub(r'\s+', ' ', normalized_text)
                # Standardize punctuation spacing
                normalized_text = re.sub(r'\s*([,.!?;:])\s*', r'\1 ', normalized_text)
                normalized_text = normalized_text.strip()
            
            if lowercase:
                normalized_text = normalized_text.lower()
            
            result = {
                "original_text": text,
                "normalized_text": normalized_text,
                "operations": {
                    "remove_diacritics": remove_diacritics,
                    "standardize_spacing": standardize_spacing,
                    "lowercase": lowercase
                },
                "character_count_change": len(normalized_text) - len(text)
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Text Normalization:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error normalizing Vietnamese text: {str(e)}")
            return [TextContent(type="text", text=f"Error normalizing Vietnamese text: {str(e)}")]

    async def _extract_vietnamese_keywords(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Extract keywords from Vietnamese text"""
        try:
            text = arguments["text"]
            max_keywords = arguments.get("max_keywords", 10)
            min_word_length = arguments.get("min_word_length", 3)
            exclude_common_words = arguments.get("exclude_common_words", True)
            
            # Simple keyword extraction
            words = re.findall(r'\b\w+\b', text.lower())
            
            # Filter by length
            words = [word for word in words if len(word) >= min_word_length]
            
            # Exclude common words if requested
            if exclude_common_words:
                words = [word for word in words if word not in self.vietnamese_common_words]
            
            # Count word frequency
            word_freq = {}
            for word in words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # Sort by frequency and get top keywords
            keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:max_keywords]
            
            result = {
                "keywords": [{"word": word, "frequency": freq} for word, freq in keywords],
                "total_words_analyzed": len(words),
                "unique_words": len(word_freq),
                "parameters": {
                    "max_keywords": max_keywords,
                    "min_word_length": min_word_length,
                    "exclude_common_words": exclude_common_words
                }
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Keywords Extraction:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error extracting Vietnamese keywords: {str(e)}")
            return [TextContent(type="text", text=f"Error extracting Vietnamese keywords: {str(e)}")]

    async def _segment_vietnamese_text(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Segment Vietnamese text"""
        try:
            text = arguments["text"]
            segment_type = arguments.get("segment_type", "sentences")
            preserve_punctuation = arguments.get("preserve_punctuation", True)
            
            result = {"original_text": text, "segmentation_type": segment_type}
            
            if segment_type in ["sentences", "both"]:
                # Simple sentence segmentation
                sentence_endings = r'[.!?]+\s*'
                sentences = re.split(sentence_endings, text)
                sentences = [s.strip() for s in sentences if s.strip()]
                result["sentences"] = sentences
                result["sentence_count"] = len(sentences)
            
            if segment_type in ["words", "both"]:
                # Word segmentation
                if preserve_punctuation:
                    words = re.findall(r'\S+', text)
                else:
                    words = re.findall(r'\b\w+\b', text)
                result["words"] = words
                result["word_count"] = len(words)
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Text Segmentation:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error segmenting Vietnamese text: {str(e)}")
            return [TextContent(type="text", text=f"Error segmenting Vietnamese text: {str(e)}")]

    async def _translate_vietnamese(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Translate Vietnamese text (placeholder implementation)"""
        try:
            text = arguments["text"]
            source_language = arguments.get("source_language", "auto")
            target_language = arguments.get("target_language", "en")
            translation_service = arguments.get("translation_service", "libre")
            
            # This is a placeholder implementation
            # In a real scenario, you would integrate with translation APIs
            
            result = {
                "original_text": text,
                "source_language": source_language,
                "target_language": target_language,
                "translation_service": translation_service,
                "translated_text": f"[PLACEHOLDER TRANSLATION] {text}",
                "confidence": 0.0,
                "note": "This is a placeholder implementation. For real translation, integrate with services like Google Translate API, LibreTranslate, or other translation services."
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Translation:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error translating Vietnamese: {str(e)}")
            return [TextContent(type="text", text=f"Error translating Vietnamese: {str(e)}")]

    async def _analyze_vietnamese_sentiment(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Analyze Vietnamese sentiment (placeholder implementation)"""
        try:
            text = arguments["text"]
            detailed_analysis = arguments.get("detailed_analysis", False)
            
            # Simple sentiment analysis based on basic patterns
            positive_words = ["tốt", "hay", "đẹp", "tuyệt", "xuất sắc", "tích cực", "vui", "hạnh phúc"]
            negative_words = ["xấu", "tệ", "dở", "kém", "tiêu cực", "buồn", "tức giận", "thất vọng"]
            
            words = re.findall(r'\b\w+\b', text.lower())
            
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)
            
            if positive_count > negative_count:
                sentiment = "positive"
                confidence = min(0.8, (positive_count - negative_count) / len(words) * 10)
            elif negative_count > positive_count:
                sentiment = "negative"
                confidence = min(0.8, (negative_count - positive_count) / len(words) * 10)
            else:
                sentiment = "neutral"
                confidence = 0.5
            
            result = {
                "text": text,
                "sentiment": sentiment,
                "confidence": round(confidence, 3),
                "word_analysis": {
                    "positive_words_found": positive_count,
                    "negative_words_found": negative_count,
                    "total_words": len(words)
                },
                "note": "This is a basic sentiment analysis. For more accurate results, consider using specialized Vietnamese NLP models."
            }
            
            if detailed_analysis:
                result["detailed_breakdown"] = {
                    "positive_indicators": [word for word in words if word in positive_words],
                    "negative_indicators": [word for word in words if word in negative_words]
                }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Sentiment Analysis:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error analyzing Vietnamese sentiment: {str(e)}")
            return [TextContent(type="text", text=f"Error analyzing Vietnamese sentiment: {str(e)}")]

    async def _validate_vietnamese_text(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Validate Vietnamese text"""
        try:
            text = arguments["text"]
            check_encoding = arguments.get("check_encoding", True)
            check_diacritics = arguments.get("check_diacritics", True)
            check_grammar = arguments.get("check_grammar", False)
            
            issues = []
            warnings = []
            
            if check_encoding:
                try:
                    text.encode('utf-8')
                except UnicodeEncodeError:
                    issues.append("Text contains characters that cannot be encoded in UTF-8")
            
            if check_diacritics:
                # Check for potential missing diacritics
                suspicious_patterns = re.findall(r'\b[aeiou]{2,}\b', text.lower())
                if suspicious_patterns:
                    warnings.append(f"Potential missing diacritics in words: {', '.join(set(suspicious_patterns))}")
            
            if check_grammar:
                # Basic grammar checks (placeholder)
                if not re.search(r'[.!?]$', text.strip()):
                    warnings.append("Text does not end with proper punctuation")
            
            result = {
                "text": text,
                "is_valid": len(issues) == 0,
                "issues": issues,
                "warnings": warnings,
                "checks_performed": {
                    "encoding": check_encoding,
                    "diacritics": check_diacritics,
                    "grammar": check_grammar
                },
                "text_stats": {
                    "character_count": len(text),
                    "word_count": len(re.findall(r'\b\w+\b', text)),
                    "vietnamese_char_count": sum(1 for char in text if char in self.vietnamese_chars)
                }
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Text Validation:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error validating Vietnamese text: {str(e)}")
            return [TextContent(type="text", text=f"Error validating Vietnamese text: {str(e)}")]

    async def _convert_vietnamese_encoding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Convert Vietnamese encoding (placeholder implementation)"""
        try:
            text = arguments["text"]
            source_encoding = arguments.get("source_encoding", "utf-8")
            target_encoding = arguments.get("target_encoding", "utf-8")
            
            # This is a placeholder implementation
            # Real encoding conversion would require proper encoding libraries
            
            result = {
                "original_text": text,
                "source_encoding": source_encoding,
                "target_encoding": target_encoding,
                "converted_text": text,  # Placeholder - no actual conversion
                "note": "This is a placeholder implementation. For real encoding conversion, use libraries like 'chardet' and proper encoding conversion utilities."
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Encoding Conversion:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error converting Vietnamese encoding: {str(e)}")
            return [TextContent(type="text", text=f"Error converting Vietnamese encoding: {str(e)}")]

    async def _generate_vietnamese_summary(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Generate Vietnamese summary (placeholder implementation)"""
        try:
            text = arguments["text"]
            summary_length = arguments.get("summary_length", "medium")
            summary_type = arguments.get("summary_type", "extractive")
            
            # Simple extractive summary - take first few sentences
            sentences = re.split(r'[.!?]+\s*', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if summary_length == "short":
                summary_sentences = sentences[:2]
            elif summary_length == "long":
                summary_sentences = sentences[:min(5, len(sentences))]
            else:  # medium
                summary_sentences = sentences[:3]
            
            summary = '. '.join(summary_sentences)
            if summary and not summary.endswith('.'):
                summary += '.'
            
            result = {
                "original_text": text,
                "summary": summary,
                "summary_length": summary_length,
                "summary_type": summary_type,
                "original_sentence_count": len(sentences),
                "summary_sentence_count": len(summary_sentences),
                "compression_ratio": round(len(summary) / len(text), 3) if text else 0,
                "note": "This is a basic extractive summary. For better results, consider using specialized Vietnamese NLP summarization models."
            }
            
            return [TextContent(
                type="text",
                text=f"Vietnamese Text Summary:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error generating Vietnamese summary: {str(e)}")
            return [TextContent(type="text", text=f"Error generating Vietnamese summary: {str(e)}")]

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="vietnamese-language",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = VietnameseLanguageMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())