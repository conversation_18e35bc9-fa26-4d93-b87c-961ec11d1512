# Brave Search MCP Server

A comprehensive MCP (Model Context Protocol) server that provides web search capabilities using the Brave Search API.

## 🚀 Features

- **Web Search**: General web search with filtering options
- **News Search**: Dedicated news article search
- **Image Search**: Image search with safe search controls
- **Video Search**: Video content search
- **URL Summarization**: Basic webpage content summarization

## 🔧 Configuration

### API Key Setup

The server is configured with API key: `BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk`

You can also set the API key via environment variable:
```bash
export BRAVE_API_KEY="your-api-key-here"
```

### Server Integration

The server is automatically integrated into the MCPO configuration at:
- **Endpoint**: `http://localhost:5000/brave_search/`
- **Configuration**: `mcp-integration/config/mcpo_config_docker.json`

## 📚 Available Tools

### 1. brave_web_search
Search the web using Brave Search API.

**Parameters:**
- `query` (required): Search query string
- `count` (optional): Number of results (1-20, default: 10)
- `offset` (optional): Pagination offset (default: 0)
- `country` (optional): Country code (default: "US")
- `freshness` (optional): Time filter ("pd", "pw", "pm", "py")
- `safesearch` (optional): Safe search level ("strict", "moderate", "off")

**Example:**
```bash
curl -X POST http://localhost:5000/brave_search/brave_web_search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer acca-enhanced-rag-mcp-key-2025" \
  -d '{
    "query": "artificial intelligence trends 2024",
    "count": 5,
    "country": "US",
    "freshness": "pm"
  }'
```

### 2. brave_news_search
Search for news articles.

**Parameters:**
- `query` (required): News search query
- `count` (optional): Number of results (1-20, default: 10)
- `offset` (optional): Pagination offset (default: 0)
- `country` (optional): Country code (default: "US")
- `freshness` (optional): Time filter ("pd", "pw", "pm")

**Example:**
```bash
curl -X POST http://localhost:5000/brave_search/brave_news_search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer acca-enhanced-rag-mcp-key-2025" \
  -d '{
    "query": "technology news",
    "count": 3,
    "freshness": "pd"
  }'
```

### 3. brave_image_search
Search for images.

**Parameters:**
- `query` (required): Image search query
- `count` (optional): Number of results (1-20, default: 10)
- `offset` (optional): Pagination offset (default: 0)
- `country` (optional): Country code (default: "US")
- `safesearch` (optional): Safe search level ("strict", "moderate", "off")

### 4. brave_video_search
Search for videos.

**Parameters:**
- `query` (required): Video search query
- `count` (optional): Number of results (1-20, default: 10)
- `offset` (optional): Pagination offset (default: 0)
- `country` (optional): Country code (default: "US")
- `safesearch` (optional): Safe search level ("strict", "moderate", "off")

### 5. brave_summarize_url
Get a summary of webpage content.

**Parameters:**
- `url` (required): URL to summarize
- `summary` (optional): Include AI summary (default: true)

## 🧪 Testing

Run the test script to verify functionality:

```bash
cd mcp-integration/servers/brave_search
python test_brave_search.py
```

## 📊 Response Format

All search tools return JSON responses with the following structure:

```json
{
  "query": "search query",
  "type": "web_search|news_search|image_search|video_search",
  "results_count": 5,
  "results": [
    {
      "title": "Result Title",
      "url": "https://example.com",
      "description": "Result description...",
      "published": "1 day ago",
      "language": "en",
      "family_friendly": true
    }
  ],
  "query_info": {
    "country": "US",
    "safesearch": "moderate",
    "freshness": "pm"
  }
}
```

## ⚠️ Rate Limits

**Important**: Brave Search API has a strict rate limit of **60 requests per minute (RPM)**.

### Rate Limit Guidelines:
- **Maximum**: 60 requests per minute
- **Recommended**: 1 request per second to stay safe
- **Best Practice**: Wait at least 1-2 seconds between requests
- **High Volume**: Implement request queuing and throttling

### Handling Rate Limits:
If you encounter rate limit errors:
- Wait 60 seconds before retrying
- Implement exponential backoff
- Use request caching to reduce API calls
- Consider batching multiple queries when possible

### Rate Limit Error Response:
```json
{
  "error": "Brave Search API rate limit exceeded"
}
```

## 🔗 Integration with Open WebUI

The Brave Search MCP server is fully integrated with your Open WebUI system and can be used through:

1. **Direct API calls** to the MCPO server
2. **MCP client integration** in your applications
3. **Open WebUI pipelines** for enhanced search capabilities

## 🛠️ Troubleshooting

### Common Issues:

1. **API Key Error**: Ensure the API key is correctly configured
2. **Rate Limit**: Wait between requests or implement request throttling
3. **Network Issues**: Check container connectivity and network configuration

### Debug Mode:

Enable debug logging by checking the server logs:
```bash
docker logs mcpo-container
```

## 📝 Dependencies

- `mcp>=1.0.0`
- `aiohttp>=3.8.0`

## 🔄 Updates

The server is automatically updated when the MCPO container is restarted. Configuration changes require container restart to take effect.