#!/usr/bin/env python3
"""
Wikipedia MCP Server
Provides Wikipedia search and content retrieval for Open WebUI integration
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional
from urllib.parse import quote, unquote

import aiohttp
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize the MCP server
server = Server("wikipedia")

# Wikipedia API configuration
WIKIPEDIA_API_BASE = "https://en.wikipedia.org/api/rest_v1"
WIKIPEDIA_API_SEARCH = "https://en.wikipedia.org/w/api.php"

class WikipediaClient:
    def __init__(self):
        self.session = None
    
    async def get_session(self):
        if self.session is None:
            self.session = aiohttp.ClientSession(
                headers={
                    'User-Agent': 'AccA-MCP-Wikipedia/1.0 (https://github.com/acca-project)'
                }
            )
        return self.session
    
    async def close(self):
        if self.session:
            await self.session.close()
            self.session = None
    
    async def search_articles(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for Wikipedia articles"""
        session = await self.get_session()
        
        params = {
            'action': 'query',
            'format': 'json',
            'list': 'search',
            'srsearch': query,
            'srlimit': limit,
            'srprop': 'snippet|titlesnippet|size|wordcount|timestamp'
        }
        
        async with session.get(WIKIPEDIA_API_SEARCH, params=params) as response:
            if response.status == 200:
                data = await response.json()
                return data.get('query', {}).get('search', [])
            else:
                raise Exception(f"Wikipedia search failed: {response.status}")
    
    async def get_article_summary(self, title: str) -> Dict[str, Any]:
        """Get article summary"""
        session = await self.get_session()
        encoded_title = quote(title.replace(' ', '_'))
        
        url = f"{WIKIPEDIA_API_BASE}/page/summary/{encoded_title}"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 404:
                raise Exception(f"Article '{title}' not found")
            else:
                raise Exception(f"Failed to get summary: {response.status}")
    
    async def get_article_content(self, title: str, section: Optional[str] = None) -> Dict[str, Any]:
        """Get full article content"""
        session = await self.get_session()
        encoded_title = quote(title.replace(' ', '_'))
        
        if section:
            url = f"{WIKIPEDIA_API_BASE}/page/mobile-sections/{encoded_title}"
        else:
            url = f"{WIKIPEDIA_API_BASE}/page/mobile-sections/{encoded_title}"
        
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 404:
                raise Exception(f"Article '{title}' not found")
            else:
                raise Exception(f"Failed to get content: {response.status}")
    
    async def get_random_articles(self, count: int = 5) -> List[Dict[str, Any]]:
        """Get random articles"""
        session = await self.get_session()
        
        params = {
            'action': 'query',
            'format': 'json',
            'list': 'random',
            'rnnamespace': 0,  # Main namespace only
            'rnlimit': count
        }
        
        async with session.get(WIKIPEDIA_API_SEARCH, params=params) as response:
            if response.status == 200:
                data = await response.json()
                random_pages = data.get('query', {}).get('random', [])
                
                # Get summaries for random pages
                summaries = []
                for page in random_pages:
                    try:
                        summary = await self.get_article_summary(page['title'])
                        summaries.append(summary)
                    except:
                        continue
                
                return summaries
            else:
                raise Exception(f"Failed to get random articles: {response.status}")
    
    async def get_page_categories(self, title: str) -> List[str]:
        """Get categories for a page"""
        session = await self.get_session()
        
        params = {
            'action': 'query',
            'format': 'json',
            'prop': 'categories',
            'titles': title,
            'cllimit': 50
        }
        
        async with session.get(WIKIPEDIA_API_SEARCH, params=params) as response:
            if response.status == 200:
                data = await response.json()
                pages = data.get('query', {}).get('pages', {})
                
                categories = []
                for page_id, page_data in pages.items():
                    if 'categories' in page_data:
                        for cat in page_data['categories']:
                            cat_title = cat['title'].replace('Category:', '')
                            categories.append(cat_title)
                
                return categories
            else:
                raise Exception(f"Failed to get categories: {response.status}")

# Global Wikipedia client
wiki_client = WikipediaClient()

def clean_html(text: str) -> str:
    """Remove HTML tags from text"""
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def format_search_results(results: List[Dict[str, Any]]) -> str:
    """Format search results for display"""
    if not results:
        return "No results found."
    
    formatted = []
    for i, result in enumerate(results, 1):
        title = result.get('title', 'Unknown')
        snippet = clean_html(result.get('snippet', ''))
        size = result.get('size', 0)
        wordcount = result.get('wordcount', 0)
        
        formatted.append(f"{i}. **{title}**")
        formatted.append(f"   {snippet}")
        formatted.append(f"   Size: {size} bytes, Words: {wordcount}")
        formatted.append("")
    
    return "\n".join(formatted)

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Wikipedia resources"""
    return [
        Resource(
            uri="wikipedia://search",
            name="Wikipedia Search",
            description="Search Wikipedia articles",
            mimeType="application/json",
        ),
        Resource(
            uri="wikipedia://random",
            name="Random Articles",
            description="Get random Wikipedia articles",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Wikipedia resource"""
    if uri == "wikipedia://search":
        return json.dumps({
            "description": "Use the search_wikipedia tool to search for articles",
            "example": "search_wikipedia('artificial intelligence')"
        }, indent=2)
    elif uri == "wikipedia://random":
        try:
            articles = await wiki_client.get_random_articles(5)
            return json.dumps(articles, indent=2)
        except Exception as e:
            return json.dumps({"error": str(e)}, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Wikipedia tools"""
    return [
        Tool(
            name="search_wikipedia",
            description="Search Wikipedia articles by query",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="get_wikipedia_summary",
            description="Get summary of a Wikipedia article",
            inputSchema={
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Article title"
                    }
                },
                "required": ["title"]
            }
        ),
        Tool(
            name="get_wikipedia_content",
            description="Get full content of a Wikipedia article",
            inputSchema={
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Article title"
                    },
                    "section": {
                        "type": "string",
                        "description": "Specific section to retrieve (optional)"
                    }
                },
                "required": ["title"]
            }
        ),
        Tool(
            name="get_random_wikipedia",
            description="Get random Wikipedia articles",
            inputSchema={
                "type": "object",
                "properties": {
                    "count": {
                        "type": "integer",
                        "description": "Number of random articles",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 20
                    }
                },
                "required": []
            }
        ),
        Tool(
            name="get_wikipedia_categories",
            description="Get categories for a Wikipedia article",
            inputSchema={
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Article title"
                    }
                },
                "required": ["title"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        if name == "search_wikipedia":
            query = arguments["query"]
            limit = arguments.get("limit", 10)
            
            results = await wiki_client.search_articles(query, limit)
            
            response = {
                "query": query,
                "results_count": len(results),
                "results": results,
                "formatted_results": format_search_results(results)
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_wikipedia_summary":
            title = arguments["title"]
            
            summary = await wiki_client.get_article_summary(title)
            
            # Clean and format the summary
            clean_summary = {
                "title": summary.get("title", title),
                "description": summary.get("description", ""),
                "extract": summary.get("extract", ""),
                "url": summary.get("content_urls", {}).get("desktop", {}).get("page", ""),
                "thumbnail": summary.get("thumbnail", {}),
                "coordinates": summary.get("coordinates", {}),
                "page_id": summary.get("pageid", ""),
                "lang": summary.get("lang", "en")
            }
            
            return [TextContent(type="text", text=json.dumps(clean_summary, indent=2))]
        
        elif name == "get_wikipedia_content":
            title = arguments["title"]
            section = arguments.get("section")
            
            content = await wiki_client.get_article_content(title, section)
            
            # Extract and format content
            sections = content.get("sections", [])
            formatted_content = {
                "title": title,
                "sections": []
            }
            
            for sect in sections:
                if sect.get("text"):
                    formatted_content["sections"].append({
                        "id": sect.get("id", ""),
                        "title": sect.get("line", ""),
                        "content": clean_html(sect.get("text", ""))
                    })
            
            return [TextContent(type="text", text=json.dumps(formatted_content, indent=2))]
        
        elif name == "get_random_wikipedia":
            count = arguments.get("count", 5)
            
            articles = await wiki_client.get_random_articles(count)
            
            response = {
                "count": len(articles),
                "articles": articles
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_wikipedia_categories":
            title = arguments["title"]
            
            categories = await wiki_client.get_page_categories(title)
            
            response = {
                "title": title,
                "categories_count": len(categories),
                "categories": categories
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main server function"""
    from mcp.types import ServerCapabilities
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="wikipedia",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )
    finally:
        await wiki_client.close()

if __name__ == "__main__":
    asyncio.run(main())