#!/usr/bin/env python3
"""
MCP Server for Gemini Search Engine
Provides search functionality using Gemini CLI Wrapper
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gemini-search-engine-mcp-server")

class GeminiSearchEngineMCPServer:
    def __init__(self):
        self.server = Server("gemini-search-engine")
        # Use gateway IP to access host services from container
        self.gemini_wrapper_endpoint = "http://**********:8010/v1/chat/completions"
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available search engine tools"""
            return [
                Tool(
                    name="search_with_gemini",
                    description="Search for information using Gemini CLI with grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query to find information about"
                            },
                            "temperature": {
                                "type": "number",
                                "description": "Temperature for response generation (0.0-1.0)",
                                "default": 0.2,
                                "minimum": 0.0,
                                "maximum": 1.0
                            },
                            "detailed": {
                                "type": "boolean",
                                "description": "Whether to provide detailed analysis with key facts",
                                "default": True
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="quick_search",
                    description="Quick search for brief information using Gemini CLI",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query for quick information"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="research_topic",
                    description="Deep research on a topic with comprehensive analysis",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Topic to research in depth"
                            },
                            "focus_areas": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific areas to focus on in the research",
                                "default": []
                            }
                        },
                        "required": ["topic"]
                    }
                ),
                Tool(
                    name="fact_check",
                    description="Fact-check a statement or claim using Gemini's grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "statement": {
                                "type": "string",
                                "description": "Statement or claim to fact-check"
                            }
                        },
                        "required": ["statement"]
                    }
                ),
                Tool(
                    name="current_events",
                    description="Get current information about recent events or news",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Topic or event to get current information about"
                            },
                            "timeframe": {
                                "type": "string",
                                "description": "Timeframe for the information (e.g., 'today', 'this week', 'recent')",
                                "default": "recent"
                            }
                        },
                        "required": ["topic"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "search_with_gemini":
                    return await self._search_with_gemini(arguments)
                elif name == "quick_search":
                    return await self._quick_search(arguments)
                elif name == "research_topic":
                    return await self._research_topic(arguments)
                elif name == "fact_check":
                    return await self._fact_check(arguments)
                elif name == "current_events":
                    return await self._current_events(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _search_with_gemini(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Search using Gemini CLI with detailed response"""
        try:
            query = arguments["query"]
            temperature = arguments.get("temperature", 0.2)
            detailed = arguments.get("detailed", True)
            
            if detailed:
                prompt = f"""You are an AI assistant acting as a highly-detailed search engine. For the given query, provide a multi-paragraph comprehensive overview. After the overview, include a section with the heading "Key Details:" followed by a bulleted list of the most important facts, figures, and specific details. Do not add any conversational fluff. Query: "{query}" """
            else:
                prompt = f"""Provide a concise but informative answer to this query: "{query}" """
            
            result = await self._call_gemini_wrapper(prompt, temperature)
            
            response_data = {
                "query": query,
                "search_type": "detailed" if detailed else "standard",
                "timestamp": datetime.now().isoformat(),
                "source": "Gemini CLI with Grounding",
                "result": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Gemini Search Results:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error searching with Gemini: {str(e)}")]

    async def _quick_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Quick search for brief information"""
        try:
            query = arguments["query"]
            prompt = f"""Provide a brief, direct answer to this query in 2-3 sentences: "{query}" """
            
            result = await self._call_gemini_wrapper(prompt, 0.1)
            
            response_data = {
                "query": query,
                "search_type": "quick",
                "timestamp": datetime.now().isoformat(),
                "answer": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Quick Search Result:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in quick search: {str(e)}")]

    async def _research_topic(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Deep research on a topic"""
        try:
            topic = arguments["topic"]
            focus_areas = arguments.get("focus_areas", [])
            
            focus_text = ""
            if focus_areas:
                focus_text = f" Pay special attention to these areas: {', '.join(focus_areas)}."
            
            prompt = f"""Conduct comprehensive research on the topic: "{topic}". Provide:
1. Overview and background
2. Current status and recent developments
3. Key statistics and data
4. Important considerations and implications
5. Future outlook or trends
{focus_text}
Structure your response with clear headings and detailed information."""
            
            result = await self._call_gemini_wrapper(prompt, 0.3)
            
            response_data = {
                "topic": topic,
                "focus_areas": focus_areas,
                "research_type": "comprehensive",
                "timestamp": datetime.now().isoformat(),
                "findings": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Research Results:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in research: {str(e)}")]

    async def _fact_check(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Fact-check a statement"""
        try:
            statement = arguments["statement"]
            
            prompt = f"""Fact-check this statement: "{statement}"
Provide:
1. Accuracy assessment (True/False/Partially True/Unclear)
2. Supporting evidence or contradicting information
3. Sources or context for verification
4. Any important nuances or clarifications
Be objective and cite specific facts."""
            
            result = await self._call_gemini_wrapper(prompt, 0.1)
            
            response_data = {
                "statement": statement,
                "fact_check_type": "verification",
                "timestamp": datetime.now().isoformat(),
                "analysis": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Fact Check Results:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in fact checking: {str(e)}")]

    async def _current_events(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get current events information"""
        try:
            topic = arguments["topic"]
            timeframe = arguments.get("timeframe", "recent")
            
            prompt = f"""Provide current information about "{topic}" focusing on {timeframe} developments. Include:
1. Latest news and updates
2. Recent changes or developments
3. Current status or situation
4. Key dates and timeline
5. Relevant statistics or data
Focus on the most recent and relevant information available."""
            
            result = await self._call_gemini_wrapper(prompt, 0.2)
            
            response_data = {
                "topic": topic,
                "timeframe": timeframe,
                "search_type": "current_events",
                "timestamp": datetime.now().isoformat(),
                "information": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Current Events Information:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error getting current events: {str(e)}")]

    async def _call_gemini_wrapper(self, prompt: str, temperature: float = 0.2) -> Dict[str, Any]:
        """Call the Gemini CLI Wrapper"""
        payload = {
            "model": "gemini-2.5-flash",
            "messages": [{"role": "user", "content": prompt}],
            "stream": False,
            "temperature": temperature
        }
        
        start_time = datetime.now()
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                self.gemini_wrapper_endpoint,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            
        end_time = datetime.now()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        data = response.json()
        content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
        
        if not content:
            raise Exception("No content returned from Gemini Wrapper")
        
        return {
            "content": content.strip(),
            "response_time_ms": response_time_ms
        }

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="gemini-search-engine",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = GeminiSearchEngineMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())