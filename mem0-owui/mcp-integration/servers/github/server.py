#!/usr/bin/env python3
"""
GitHub MCP Server
Provides GitHub API integration for Open WebUI
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import quote

import aiohttp
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize the MCP server
server = Server("github")

# GitHub API configuration
GITHUB_API_BASE = "https://api.github.com"

class GitHubClient:
    def __init__(self, token: Optional[str] = None):
        self.token = token or os.getenv('GITHUB_TOKEN')
        self.session = None
    
    async def get_session(self):
        if self.session is None:
            headers = {
                'User-Agent': 'AccA-MCP-GitHub/1.0',
                'Accept': 'application/vnd.github.v3+json'
            }
            if self.token:
                headers['Authorization'] = f'token {self.token}'
            
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def close(self):
        if self.session:
            await self.session.close()
            self.session = None
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make GitHub API request"""
        session = await self.get_session()
        url = f"{GITHUB_API_BASE}{endpoint}"
        
        async with session.request(method, url, **kwargs) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 404:
                raise Exception(f"Resource not found: {endpoint}")
            elif response.status == 403:
                raise Exception("GitHub API rate limit exceeded or access denied")
            elif response.status == 401:
                raise Exception("GitHub authentication failed - check your token")
            else:
                error_text = await response.text()
                raise Exception(f"GitHub API error {response.status}: {error_text}")
    
    async def search_repositories(self, query: str, sort: str = "stars", order: str = "desc", per_page: int = 10) -> Dict[str, Any]:
        """Search GitHub repositories"""
        params = {
            'q': query,
            'sort': sort,
            'order': order,
            'per_page': per_page
        }
        return await self.make_request('GET', '/search/repositories', params=params)
    
    async def get_repository(self, owner: str, repo: str) -> Dict[str, Any]:
        """Get repository information"""
        return await self.make_request('GET', f'/repos/{owner}/{repo}')
    
    async def get_repository_contents(self, owner: str, repo: str, path: str = "") -> List[Dict[str, Any]]:
        """Get repository contents"""
        endpoint = f'/repos/{owner}/{repo}/contents'
        if path:
            endpoint += f'/{path}'
        return await self.make_request('GET', endpoint)
    
    async def get_file_content(self, owner: str, repo: str, path: str) -> Dict[str, Any]:
        """Get file content from repository"""
        return await self.make_request('GET', f'/repos/{owner}/{repo}/contents/{path}')
    
    async def get_repository_issues(self, owner: str, repo: str, state: str = "open", per_page: int = 10) -> List[Dict[str, Any]]:
        """Get repository issues"""
        params = {
            'state': state,
            'per_page': per_page
        }
        return await self.make_request('GET', f'/repos/{owner}/{repo}/issues', params=params)
    
    async def get_repository_pulls(self, owner: str, repo: str, state: str = "open", per_page: int = 10) -> List[Dict[str, Any]]:
        """Get repository pull requests"""
        params = {
            'state': state,
            'per_page': per_page
        }
        return await self.make_request('GET', f'/repos/{owner}/{repo}/pulls', params=params)
    
    async def get_repository_commits(self, owner: str, repo: str, per_page: int = 10) -> List[Dict[str, Any]]:
        """Get repository commits"""
        params = {'per_page': per_page}
        return await self.make_request('GET', f'/repos/{owner}/{repo}/commits', params=params)
    
    async def get_user_info(self, username: str) -> Dict[str, Any]:
        """Get user information"""
        return await self.make_request('GET', f'/users/{username}')
    
    async def get_user_repositories(self, username: str, per_page: int = 10) -> List[Dict[str, Any]]:
        """Get user repositories"""
        params = {'per_page': per_page}
        return await self.make_request('GET', f'/users/{username}/repos', params=params)
    
    async def get_repository_releases(self, owner: str, repo: str, per_page: int = 10) -> List[Dict[str, Any]]:
        """Get repository releases"""
        params = {'per_page': per_page}
        return await self.make_request('GET', f'/repos/{owner}/{repo}/releases', params=params)
    
    async def get_repository_languages(self, owner: str, repo: str) -> Dict[str, int]:
        """Get repository programming languages"""
        return await self.make_request('GET', f'/repos/{owner}/{repo}/languages')
    
    async def search_code(self, query: str, per_page: int = 10) -> Dict[str, Any]:
        """Search code in GitHub"""
        params = {
            'q': query,
            'per_page': per_page
        }
        return await self.make_request('GET', '/search/code', params=params)

# Global GitHub client
github_client = GitHubClient()

def format_repository_info(repo: Dict[str, Any]) -> Dict[str, Any]:
    """Format repository information"""
    return {
        "name": repo.get("name"),
        "full_name": repo.get("full_name"),
        "description": repo.get("description"),
        "url": repo.get("html_url"),
        "clone_url": repo.get("clone_url"),
        "language": repo.get("language"),
        "stars": repo.get("stargazers_count", 0),
        "forks": repo.get("forks_count", 0),
        "issues": repo.get("open_issues_count", 0),
        "created_at": repo.get("created_at"),
        "updated_at": repo.get("updated_at"),
        "size": repo.get("size", 0),
        "default_branch": repo.get("default_branch"),
        "topics": repo.get("topics", []),
        "license": repo.get("license", {}).get("name") if repo.get("license") else None
    }

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available GitHub resources"""
    return [
        Resource(
            uri="github://trending",
            name="GitHub Trending",
            description="Trending repositories on GitHub",
            mimeType="application/json",
        ),
        Resource(
            uri="github://search",
            name="GitHub Search",
            description="Search GitHub repositories and code",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read GitHub resource"""
    if uri == "github://trending":
        try:
            # Get trending repositories (most starred in the last week)
            trending = await github_client.search_repositories(
                "created:>2024-01-01", 
                sort="stars", 
                order="desc", 
                per_page=10
            )
            return json.dumps(trending, indent=2)
        except Exception as e:
            return json.dumps({"error": str(e)}, indent=2)
    
    elif uri == "github://search":
        return json.dumps({
            "description": "Use the search_github_repositories tool to search for repositories",
            "example": "search_github_repositories('machine learning python')"
        }, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available GitHub tools"""
    return [
        Tool(
            name="search_github_repositories",
            description="Search GitHub repositories",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "sort": {
                        "type": "string",
                        "description": "Sort by: stars, forks, help-wanted-issues, updated",
                        "default": "stars"
                    },
                    "order": {
                        "type": "string",
                        "description": "Sort order: asc, desc",
                        "default": "desc"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Results per page (max 100)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="get_github_repository",
            description="Get detailed information about a GitHub repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner (username or organization)"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    }
                },
                "required": ["owner", "repo"]
            }
        ),
        Tool(
            name="get_repository_contents",
            description="Get contents of a repository directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "path": {
                        "type": "string",
                        "description": "Directory path (empty for root)",
                        "default": ""
                    }
                },
                "required": ["owner", "repo"]
            }
        ),
        Tool(
            name="get_file_content",
            description="Get content of a specific file from repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "path": {
                        "type": "string",
                        "description": "File path"
                    }
                },
                "required": ["owner", "repo", "path"]
            }
        ),
        Tool(
            name="get_repository_issues",
            description="Get issues from a repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "state": {
                        "type": "string",
                        "description": "Issue state: open, closed, all",
                        "default": "open"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of issues to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["owner", "repo"]
            }
        ),
        Tool(
            name="get_repository_pulls",
            description="Get pull requests from a repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "state": {
                        "type": "string",
                        "description": "PR state: open, closed, all",
                        "default": "open"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of PRs to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["owner", "repo"]
            }
        ),
        Tool(
            name="get_repository_commits",
            description="Get recent commits from a repository",
            inputSchema={
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of commits to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["owner", "repo"]
            }
        ),
        Tool(
            name="get_user_info",
            description="Get information about a GitHub user",
            inputSchema={
                "type": "object",
                "properties": {
                    "username": {
                        "type": "string",
                        "description": "GitHub username"
                    }
                },
                "required": ["username"]
            }
        ),
        Tool(
            name="get_user_repositories",
            description="Get repositories owned by a user",
            inputSchema={
                "type": "object",
                "properties": {
                    "username": {
                        "type": "string",
                        "description": "GitHub username"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of repositories to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["username"]
            }
        ),
        Tool(
            name="search_github_code",
            description="Search code across GitHub repositories",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Code search query"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of results to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        if name == "search_github_repositories":
            query = arguments["query"]
            sort = arguments.get("sort", "stars")
            order = arguments.get("order", "desc")
            per_page = arguments.get("per_page", 10)
            
            results = await github_client.search_repositories(query, sort, order, per_page)
            
            # Format results
            formatted_repos = []
            for repo in results.get("items", []):
                formatted_repos.append(format_repository_info(repo))
            
            response = {
                "query": query,
                "total_count": results.get("total_count", 0),
                "results_count": len(formatted_repos),
                "repositories": formatted_repos
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_github_repository":
            owner = arguments["owner"]
            repo = arguments["repo"]
            
            repo_info = await github_client.get_repository(owner, repo)
            formatted_repo = format_repository_info(repo_info)
            
            # Get additional info
            try:
                languages = await github_client.get_repository_languages(owner, repo)
                formatted_repo["languages"] = languages
            except:
                pass
            
            return [TextContent(type="text", text=json.dumps(formatted_repo, indent=2))]
        
        elif name == "get_repository_contents":
            owner = arguments["owner"]
            repo = arguments["repo"]
            path = arguments.get("path", "")
            
            contents = await github_client.get_repository_contents(owner, repo, path)
            
            # Format contents
            formatted_contents = []
            for item in contents:
                formatted_contents.append({
                    "name": item.get("name"),
                    "path": item.get("path"),
                    "type": item.get("type"),
                    "size": item.get("size"),
                    "download_url": item.get("download_url"),
                    "html_url": item.get("html_url")
                })
            
            response = {
                "owner": owner,
                "repo": repo,
                "path": path,
                "contents": formatted_contents
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_file_content":
            owner = arguments["owner"]
            repo = arguments["repo"]
            path = arguments["path"]
            
            file_info = await github_client.get_file_content(owner, repo, path)
            
            # Decode content if it's base64 encoded
            import base64
            content = file_info.get("content", "")
            if file_info.get("encoding") == "base64":
                try:
                    decoded_content = base64.b64decode(content).decode('utf-8')
                except:
                    decoded_content = "Binary file or encoding error"
            else:
                decoded_content = content
            
            response = {
                "owner": owner,
                "repo": repo,
                "path": path,
                "name": file_info.get("name"),
                "size": file_info.get("size"),
                "content": decoded_content,
                "download_url": file_info.get("download_url"),
                "html_url": file_info.get("html_url")
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_repository_issues":
            owner = arguments["owner"]
            repo = arguments["repo"]
            state = arguments.get("state", "open")
            per_page = arguments.get("per_page", 10)
            
            issues = await github_client.get_repository_issues(owner, repo, state, per_page)
            
            # Format issues
            formatted_issues = []
            for issue in issues:
                formatted_issues.append({
                    "number": issue.get("number"),
                    "title": issue.get("title"),
                    "state": issue.get("state"),
                    "user": issue.get("user", {}).get("login"),
                    "created_at": issue.get("created_at"),
                    "updated_at": issue.get("updated_at"),
                    "comments": issue.get("comments", 0),
                    "labels": [label.get("name") for label in issue.get("labels", [])],
                    "html_url": issue.get("html_url")
                })
            
            response = {
                "owner": owner,
                "repo": repo,
                "state": state,
                "issues_count": len(formatted_issues),
                "issues": formatted_issues
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_repository_pulls":
            owner = arguments["owner"]
            repo = arguments["repo"]
            state = arguments.get("state", "open")
            per_page = arguments.get("per_page", 10)
            
            pulls = await github_client.get_repository_pulls(owner, repo, state, per_page)
            
            # Format pull requests
            formatted_pulls = []
            for pr in pulls:
                formatted_pulls.append({
                    "number": pr.get("number"),
                    "title": pr.get("title"),
                    "state": pr.get("state"),
                    "user": pr.get("user", {}).get("login"),
                    "created_at": pr.get("created_at"),
                    "updated_at": pr.get("updated_at"),
                    "mergeable": pr.get("mergeable"),
                    "draft": pr.get("draft"),
                    "html_url": pr.get("html_url")
                })
            
            response = {
                "owner": owner,
                "repo": repo,
                "state": state,
                "pulls_count": len(formatted_pulls),
                "pulls": formatted_pulls
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_repository_commits":
            owner = arguments["owner"]
            repo = arguments["repo"]
            per_page = arguments.get("per_page", 10)
            
            commits = await github_client.get_repository_commits(owner, repo, per_page)
            
            # Format commits
            formatted_commits = []
            for commit in commits:
                commit_info = commit.get("commit", {})
                formatted_commits.append({
                    "sha": commit.get("sha"),
                    "message": commit_info.get("message"),
                    "author": commit_info.get("author", {}).get("name"),
                    "date": commit_info.get("author", {}).get("date"),
                    "html_url": commit.get("html_url")
                })
            
            response = {
                "owner": owner,
                "repo": repo,
                "commits_count": len(formatted_commits),
                "commits": formatted_commits
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_user_info":
            username = arguments["username"]
            
            user_info = await github_client.get_user_info(username)
            
            formatted_user = {
                "login": user_info.get("login"),
                "name": user_info.get("name"),
                "bio": user_info.get("bio"),
                "company": user_info.get("company"),
                "location": user_info.get("location"),
                "email": user_info.get("email"),
                "blog": user_info.get("blog"),
                "public_repos": user_info.get("public_repos", 0),
                "followers": user_info.get("followers", 0),
                "following": user_info.get("following", 0),
                "created_at": user_info.get("created_at"),
                "html_url": user_info.get("html_url")
            }
            
            return [TextContent(type="text", text=json.dumps(formatted_user, indent=2))]
        
        elif name == "get_user_repositories":
            username = arguments["username"]
            per_page = arguments.get("per_page", 10)
            
            repos = await github_client.get_user_repositories(username, per_page)
            
            # Format repositories
            formatted_repos = []
            for repo in repos:
                formatted_repos.append(format_repository_info(repo))
            
            response = {
                "username": username,
                "repos_count": len(formatted_repos),
                "repositories": formatted_repos
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "search_github_code":
            query = arguments["query"]
            per_page = arguments.get("per_page", 10)
            
            results = await github_client.search_code(query, per_page)
            
            # Format code search results
            formatted_results = []
            for item in results.get("items", []):
                formatted_results.append({
                    "name": item.get("name"),
                    "path": item.get("path"),
                    "repository": item.get("repository", {}).get("full_name"),
                    "html_url": item.get("html_url"),
                    "score": item.get("score")
                })
            
            response = {
                "query": query,
                "total_count": results.get("total_count", 0),
                "results_count": len(formatted_results),
                "code_results": formatted_results
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main server function"""
    from mcp.types import ServerCapabilities
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="github",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )
    finally:
        await github_client.close()

if __name__ == "__main__":
    asyncio.run(main())