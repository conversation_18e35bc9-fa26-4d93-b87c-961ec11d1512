#!/usr/bin/env python3
"""
Debug Gemini processing issue
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

async def debug_gemini_processing():
    """Debug Gemini processing step by step"""
    print("🔍 DEBUG: Gemini Processing Issue")
    print("=" * 60)
    
    crawler = JiniCrawler()
    
    try:
        # Initialize
        await crawler.initialize()
        
        # Test URL
        test_url = "https://dantri.com.vn/the-gioi.htm"
        print(f"🕷️ Testing: {test_url}")
        
        # Step 1: Check raw crawl result
        print(f"\n📋 STEP 1: Raw Jina Crawler Result")
        print("-" * 40)
        
        crawl_result = None
        async for snapshot in crawler.jina_crawler.scrap_url(test_url):
            if not snapshot.is_intermediate:
                crawl_result = snapshot
                break
        
        if crawl_result:
            print(f"✅ Raw HTML length: {len(crawl_result.html)} chars")
            print(f"✅ Title: {crawl_result.title}")
            print(f"✅ Text length: {len(crawl_result.text)} chars")
            
            # Show first 500 chars of HTML
            print(f"\n📄 HTML Preview (first 500 chars):")
            print(crawl_result.html[:500])
            print("...")
            
            # Show first 500 chars of text
            print(f"\n📝 Text Preview (first 500 chars):")
            print(crawl_result.text[:500])
            print("...")
        else:
            print("❌ No crawl result")
            return
        
        # Step 2: Check content sent to Gemini
        print(f"\n📋 STEP 2: Content Sent to Gemini")
        print("-" * 40)
        
        max_content_length = 10000
        content_to_process = crawl_result.html[:max_content_length]
        print(f"✅ Content length sent to Gemini: {len(content_to_process)} chars")
        
        # Show what's actually sent
        print(f"\n📤 Content sent to Gemini (first 800 chars):")
        print(content_to_process[:800])
        print("...")
        
        # Step 3: Test Gemini directly
        print(f"\n📋 STEP 3: Direct Gemini Test")
        print("-" * 40)
        
        gemini_result = await crawler.gemini_processor.process_content(
            content_to_process, "html_to_markdown"
        )
        
        print(f"✅ Gemini success: {gemini_result.get('success')}")
        print(f"✅ Processing time: {gemini_result.get('processing_time', 0):.2f}s")
        print(f"✅ Input length: {gemini_result.get('original_length', 0)}")
        print(f"✅ Output length: {gemini_result.get('output_length', 0)}")
        
        if gemini_result.get('success'):
            processed_content = gemini_result.get('processed_content', '')
            print(f"\n📝 Gemini Output:")
            print(processed_content)
        else:
            print(f"❌ Gemini Error: {gemini_result.get('error', 'Unknown')}")
        
        # Step 4: Test with different content
        print(f"\n📋 STEP 4: Test with Simple HTML")
        print("-" * 40)
        
        simple_html = """
        <html>
        <head><title>Test Article</title></head>
        <body>
        <h1>Tin tức công nghệ Việt Nam</h1>
        <p>Trí tuệ nhân tạo đang phát triển mạnh mẽ tại Việt Nam. Nhiều công ty công nghệ đã bắt đầu ứng dụng AI vào sản phẩm của mình.</p>
        <p>Theo báo cáo mới nhất, thị trường AI Việt Nam dự kiến sẽ tăng trưởng 25% trong năm 2024.</p>
        <h2>Các ứng dụng AI phổ biến</h2>
        <ul>
        <li>Chatbot hỗ trợ khách hàng</li>
        <li>Phân tích dữ liệu</li>
        <li>Xử lý ngôn ngữ tự nhiên</li>
        </ul>
        </body>
        </html>
        """
        
        simple_result = await crawler.gemini_processor.process_content(
            simple_html, "html_to_markdown"
        )
        
        print(f"✅ Simple test success: {simple_result.get('success')}")
        if simple_result.get('success'):
            print(f"📝 Simple test output:")
            print(simple_result.get('processed_content', ''))
        else:
            print(f"❌ Simple test error: {simple_result.get('error', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await crawler.cleanup()

if __name__ == "__main__":
    asyncio.run(debug_gemini_processing())