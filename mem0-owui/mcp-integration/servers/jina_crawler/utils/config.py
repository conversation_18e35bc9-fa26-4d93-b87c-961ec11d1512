"""
Configuration management for the ReaderLM Web Crawler.
Loads environment variables and provides configuration settings.
"""

import os
from typing import Op<PERSON>
from dataclasses import dataclass


@dataclass
class DatabaseConfig:
    """Oracle Database configuration."""
    host: str
    port: int
    service_name: str
    username: str
    password: str
    pool_min: int = 2
    pool_max: int = 10
    pool_increment: int = 1


@dataclass
class APIConfig:
    """API server configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False
    log_level: str = "info"


@dataclass
class RateLimitConfig:
    """Rate limiting configuration."""
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_limit: int = 10


@dataclass
class CacheConfig:
    """Cache configuration."""
    default_ttl: int = 3600  # 1 hour
    max_content_size: int = 1024 * 1024  # 1MB
    cleanup_interval: int = 3600  # 1 hour


@dataclass
class SecurityConfig:
    """Security configuration."""
    api_key_header: str = "X-API-Key"
    cors_origins: list = None
    cors_methods: list = None
    cors_headers: list = None


@dataclass
class ModelConfig:
    """Model configuration."""
    model_name: str = "jinaai/ReaderLM-v2"
    device: str = "cpu"
    torch_dtype: str = "float16"
    max_length: int = 2048
    temperature: float = 0.7
    do_sample: bool = True
    gemini_api_key: Optional[str] = None


@dataclass
class CrawlerConfig:
    """Web crawler configuration."""
    timeout: int = 30000  # 30 seconds
    wait_for_selector: Optional[str] = None
    wait_for_load_state: str = "networkidle"
    user_agent: str = "ReaderLM-Crawler/1.0"
    viewport_width: int = 1280
    viewport_height: int = 720
    max_redirects: int = 5


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


class Config:
    """Main configuration class that loads all settings."""
    
    def __init__(self):
        self.database = self._load_database_config()
        self.api = self._load_api_config()
        self.rate_limit = self._load_rate_limit_config()
        self.cache = self._load_cache_config()
        self.security = self._load_security_config()
        self.model = self._load_model_config()
        self.crawler = self._load_crawler_config()
        self.logging = self._load_logging_config()
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration from environment variables."""
        return DatabaseConfig(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "1521")),
            service_name=os.getenv("DB_SERVICE_NAME", "XEPDB1"),
            username=os.getenv("DB_USERNAME", "crawler_user"),
            password=os.getenv("DB_PASSWORD", ""),
            pool_min=int(os.getenv("DB_POOL_MIN", "2")),
            pool_max=int(os.getenv("DB_POOL_MAX", "10")),
            pool_increment=int(os.getenv("DB_POOL_INCREMENT", "1"))
        )
    
    def _load_api_config(self) -> APIConfig:
        """Load API configuration from environment variables."""
        return APIConfig(
            host=os.getenv("API_HOST", "0.0.0.0"),
            port=int(os.getenv("API_PORT", "8000")),
            workers=int(os.getenv("API_WORKERS", "1")),
            reload=os.getenv("API_RELOAD", "false").lower() == "true",
            log_level=os.getenv("API_LOG_LEVEL", "info")
        )
    
    def _load_rate_limit_config(self) -> RateLimitConfig:
        """Load rate limiting configuration from environment variables."""
        return RateLimitConfig(
            requests_per_minute=int(os.getenv("RATE_LIMIT_PER_MINUTE", "60")),
            requests_per_hour=int(os.getenv("RATE_LIMIT_PER_HOUR", "1000")),
            requests_per_day=int(os.getenv("RATE_LIMIT_PER_DAY", "10000")),
            burst_limit=int(os.getenv("RATE_LIMIT_BURST", "10"))
        )
    
    def _load_cache_config(self) -> CacheConfig:
        """Load cache configuration from environment variables."""
        return CacheConfig(
            default_ttl=int(os.getenv("CACHE_DEFAULT_TTL", "3600")),
            max_content_size=int(os.getenv("CACHE_MAX_CONTENT_SIZE", str(1024 * 1024))),
            cleanup_interval=int(os.getenv("CACHE_CLEANUP_INTERVAL", "3600"))
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """Load security configuration from environment variables."""
        cors_origins = os.getenv("CORS_ORIGINS", "*").split(",") if os.getenv("CORS_ORIGINS") else ["*"]
        cors_methods = os.getenv("CORS_METHODS", "GET,POST").split(",") if os.getenv("CORS_METHODS") else ["GET", "POST"]
        cors_headers = os.getenv("CORS_HEADERS", "*").split(",") if os.getenv("CORS_HEADERS") else ["*"]
        
        return SecurityConfig(
            api_key_header=os.getenv("API_KEY_HEADER", "X-API-Key"),
            cors_origins=cors_origins,
            cors_methods=cors_methods,
            cors_headers=cors_headers
        )
    
    def _load_model_config(self) -> ModelConfig:
        """Load model configuration from environment variables."""
        return ModelConfig(
            model_name=os.getenv("MODEL_NAME", "jinaai/ReaderLM-v2"),
            device=os.getenv("MODEL_DEVICE", "cpu"),
            torch_dtype=os.getenv("MODEL_TORCH_DTYPE", "float16"),
            max_length=int(os.getenv("MODEL_MAX_LENGTH", "2048")),
            temperature=float(os.getenv("MODEL_TEMPERATURE", "0.7")),
            do_sample=os.getenv("MODEL_DO_SAMPLE", "true").lower() == "true",
            gemini_api_key=os.getenv("GEMINI_API_KEY")
        )
    
    def _load_crawler_config(self) -> CrawlerConfig:
        """Load crawler configuration from environment variables."""
        return CrawlerConfig(
            timeout=int(os.getenv("CRAWLER_TIMEOUT", "30000")),
            wait_for_selector=os.getenv("CRAWLER_WAIT_FOR_SELECTOR"),
            wait_for_load_state=os.getenv("CRAWLER_WAIT_FOR_LOAD_STATE", "networkidle"),
            user_agent=os.getenv("CRAWLER_USER_AGENT", "ReaderLM-Crawler/1.0"),
            viewport_width=int(os.getenv("CRAWLER_VIEWPORT_WIDTH", "1280")),
            viewport_height=int(os.getenv("CRAWLER_VIEWPORT_HEIGHT", "720")),
            max_redirects=int(os.getenv("CRAWLER_MAX_REDIRECTS", "5"))
        )
    
    def _load_logging_config(self) -> LoggingConfig:
        """Load logging configuration from environment variables."""
        return LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            file_path=os.getenv("LOG_FILE_PATH"),
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )


# Global configuration instance
config = Config()