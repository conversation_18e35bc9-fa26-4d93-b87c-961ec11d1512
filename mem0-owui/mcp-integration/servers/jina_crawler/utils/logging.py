"""
Logging configuration for the ReaderLM Web Crawler.
Sets up structured logging with file rotation and console output.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from .config import config


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output."""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging(
    name: str = "readerlm_crawler",
    level: Optional[str] = None,
    log_file: Optional[str] = None,
    console_output: bool = True
) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (optional)
        console_output: Whether to output to console
    
    Returns:
        Configured logger instance
    """
    # Use config values if not provided
    level = level or config.logging.level
    log_file = log_file or config.logging.file_path
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, level.upper()))
        
        # Use colored formatter for console
        console_formatter = ColoredFormatter(config.logging.format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Create log directory if it doesn't exist
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        
        # Use standard formatter for file
        file_formatter = logging.Formatter(config.logging.format)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
    
    Returns:
        Logger instance
    """
    return logging.getLogger(f"readerlm_crawler.{name}")


# Create main application logger
main_logger = setup_logging()


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_request(logger: logging.Logger, method: str, url: str, status_code: int, duration: float):
    """
    Log HTTP request information.
    
    Args:
        logger: Logger instance
        method: HTTP method
        url: Request URL
        status_code: Response status code
        duration: Request duration in seconds
    """
    logger.info(
        f"{method} {url} - {status_code} - {duration:.3f}s",
        extra={
            'method': method,
            'url': url,
            'status_code': status_code,
            'duration': duration
        }
    )


def log_error(logger: logging.Logger, error: Exception, context: Optional[dict] = None):
    """
    Log error with context information.
    
    Args:
        logger: Logger instance
        error: Exception instance
        context: Additional context information
    """
    context = context or {}
    logger.error(
        f"Error: {str(error)}",
        extra={
            'error_type': type(error).__name__,
            'error_message': str(error),
            **context
        },
        exc_info=True
    )


def log_performance(logger: logging.Logger, operation: str, duration: float, **kwargs):
    """
    Log performance metrics.
    
    Args:
        logger: Logger instance
        operation: Operation name
        duration: Operation duration in seconds
        **kwargs: Additional metrics
    """
    logger.info(
        f"Performance: {operation} completed in {duration:.3f}s",
        extra={
            'operation': operation,
            'duration': duration,
            **kwargs
        }
    )