#!/usr/bin/env python3
"""
Content Processor - HTML to Markdown conversion using ReaderLM-v2 style processing
Alternative to Jina AI for content cleaning and formatting
"""
import re
import logging
from typing import Optional, Dict, Any
from bs4 import BeautifulSoup, Comment
from urllib.parse import urljoin, urlparse
import html

logger = logging.getLogger(__name__)

class ContentProcessor:
    """Process raw HTML content into clean markdown"""
    
    def __init__(self):
        self.stats = {
            'processed_count': 0,
            'avg_reduction_ratio': 0.0,
            'total_input_size': 0,
            'total_output_size': 0
        }
    
    def process_html_to_markdown(self, html_content: str, base_url: str = "") -> Dict[str, Any]:
        """
        Convert HTML to clean markdown
        
        Args:
            html_content: Raw HTML content
            base_url: Base URL for resolving relative links
            
        Returns:
            Dict with processed content and metadata
        """
        try:
            # Parse HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Clean HTML
            cleaned_soup = self._clean_html(soup)
            
            # Extract main content
            main_content = self._extract_main_content(cleaned_soup)
            
            # Convert to markdown
            markdown = self._html_to_markdown(main_content, base_url)
            
            # Update stats
            self._update_stats(len(html_content), len(markdown))
            
            # Extract metadata
            metadata = self._extract_metadata(soup)
            
            return {
                'success': True,
                'content': markdown,
                'metadata': metadata,
                'original_size': len(html_content),
                'processed_size': len(markdown),
                'reduction_ratio': 1 - (len(markdown) / max(len(html_content), 1))
            }
            
        except Exception as e:
            logger.error(f"❌ Content processing error: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': html_content,  # Fallback to original
                'metadata': {}
            }
    
    def _clean_html(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Remove unwanted elements from HTML"""
        
        # Remove comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # Remove script and style tags
        for tag in soup.find_all(['script', 'style', 'noscript']):
            tag.decompose()
        
        # Remove common noise elements
        noise_selectors = [
            'nav', 'header', 'footer', 'aside',
            '.advertisement', '.ads', '.ad',
            '.social-share', '.social-media',
            '.cookie-notice', '.cookie-banner',
            '.popup', '.modal', '.overlay',
            '.sidebar', '.widget',
            '.breadcrumb', '.pagination',
            '.related-posts', '.recommended',
            '.comments-section', '.comment-form',
            '.newsletter-signup', '.subscription',
            '.tracking', '.analytics'
        ]
        
        for selector in noise_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        # Remove elements with common noise attributes
        noise_attrs = [
            'data-ad', 'data-advertisement',
            'data-tracking', 'data-analytics',
            'data-social', 'data-share'
        ]
        
        for attr in noise_attrs:
            for element in soup.find_all(attrs={attr: True}):
                element.decompose()
        
        return soup
    
    def _extract_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Extract main content from cleaned HTML"""
        
        # Try to find main content containers
        main_selectors = [
            'main',
            'article',
            '.main-content',
            '.content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '#main'
        ]
        
        for selector in main_selectors:
            main_element = soup.select_one(selector)
            if main_element and self._has_substantial_content(main_element):
                return main_element
        
        # Fallback: find the largest text container
        text_containers = soup.find_all(['div', 'section', 'article'])
        if text_containers:
            # Score containers by text length
            scored_containers = []
            for container in text_containers:
                text_length = len(container.get_text(strip=True))
                if text_length > 100:  # Minimum content threshold
                    scored_containers.append((container, text_length))
            
            if scored_containers:
                # Return container with most text
                best_container = max(scored_containers, key=lambda x: x[1])[0]
                return best_container
        
        # Final fallback: return body
        return soup.find('body') or soup
    
    def _has_substantial_content(self, element) -> bool:
        """Check if element has substantial text content"""
        text = element.get_text(strip=True)
        return len(text) > 200 and len(text.split()) > 30
    
    def _html_to_markdown(self, soup: BeautifulSoup, base_url: str = "") -> str:
        """Convert HTML soup to markdown"""
        markdown_parts = []
        
        for element in soup.descendants:
            if element.name is None:  # Text node
                text = str(element).strip()
                if text:
                    markdown_parts.append(text)
            
            elif element.name == 'h1':
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"\n# {text}\n")
            
            elif element.name == 'h2':
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"\n## {text}\n")
            
            elif element.name == 'h3':
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"\n### {text}\n")
            
            elif element.name in ['h4', 'h5', 'h6']:
                text = element.get_text(strip=True)
                if text:
                    level = int(element.name[1])
                    markdown_parts.append(f"\n{'#' * level} {text}\n")
            
            elif element.name == 'p':
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"\n{text}\n")
            
            elif element.name == 'a':
                text = element.get_text(strip=True)
                href = element.get('href', '')
                if text and href:
                    # Resolve relative URLs
                    if base_url and not href.startswith(('http://', 'https://', 'mailto:', 'tel:')):
                        href = urljoin(base_url, href)
                    markdown_parts.append(f"[{text}]({href})")
                elif text:
                    markdown_parts.append(text)
            
            elif element.name in ['strong', 'b']:
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"**{text}**")
            
            elif element.name in ['em', 'i']:
                text = element.get_text(strip=True)
                if text:
                    markdown_parts.append(f"*{text}*")
            
            elif element.name == 'code':
                text = element.get_text()
                if text:
                    markdown_parts.append(f"`{text}`")
            
            elif element.name == 'pre':
                text = element.get_text()
                if text:
                    markdown_parts.append(f"\n```\n{text}\n```\n")
            
            elif element.name in ['ul', 'ol']:
                # Handle lists
                list_items = element.find_all('li', recursive=False)
                if list_items:
                    markdown_parts.append("\n")
                    for i, li in enumerate(list_items):
                        text = li.get_text(strip=True)
                        if text:
                            if element.name == 'ul':
                                markdown_parts.append(f"- {text}\n")
                            else:
                                markdown_parts.append(f"{i+1}. {text}\n")
                    markdown_parts.append("\n")
            
            elif element.name == 'br':
                markdown_parts.append("\n")
            
            elif element.name == 'hr':
                markdown_parts.append("\n---\n")
            
            elif element.name == 'blockquote':
                text = element.get_text(strip=True)
                if text:
                    # Split into lines and add > prefix
                    lines = text.split('\n')
                    quoted_lines = [f"> {line}" for line in lines if line.strip()]
                    markdown_parts.append(f"\n{chr(10).join(quoted_lines)}\n")
        
        # Join and clean up markdown
        markdown = ''.join(markdown_parts)
        
        # Clean up excessive whitespace
        markdown = re.sub(r'\n{3,}', '\n\n', markdown)
        markdown = re.sub(r'[ \t]+', ' ', markdown)
        markdown = markdown.strip()
        
        return markdown
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract metadata from HTML"""
        metadata = {}
        
        # Title
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.get_text(strip=True)
        
        # Meta tags
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            
            if name and content:
                # Common meta tags
                if name in ['description', 'keywords', 'author']:
                    metadata[name] = content
                elif name.startswith('og:'):
                    metadata[name] = content
                elif name.startswith('twitter:'):
                    metadata[name] = content
        
        # Language
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            metadata['language'] = html_tag.get('lang')
        
        # Canonical URL
        canonical = soup.find('link', rel='canonical')
        if canonical and canonical.get('href'):
            metadata['canonical_url'] = canonical.get('href')
        
        return metadata
    
    def _update_stats(self, input_size: int, output_size: int):
        """Update processing statistics"""
        self.stats['processed_count'] += 1
        self.stats['total_input_size'] += input_size
        self.stats['total_output_size'] += output_size
        
        # Calculate average reduction ratio
        if self.stats['total_input_size'] > 0:
            self.stats['avg_reduction_ratio'] = 1 - (
                self.stats['total_output_size'] / self.stats['total_input_size']
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            **self.stats,
            'avg_input_size': self.stats['total_input_size'] / max(self.stats['processed_count'], 1),
            'avg_output_size': self.stats['total_output_size'] / max(self.stats['processed_count'], 1)
        }

# Convenience function
def process_html_content(html_content: str, base_url: str = "") -> Dict[str, Any]:
    """Quick HTML to markdown processing"""
    processor = ContentProcessor()
    return processor.process_html_to_markdown(html_content, base_url)

# Test function
def test_content_processor():
    """Test content processor"""
    print("🧪 Testing Content Processor")
    print("=" * 50)
    
    # Test HTML
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Article</title>
        <meta name="description" content="A test article">
    </head>
    <body>
        <nav>Navigation</nav>
        <header>Header</header>
        <main>
            <article>
                <h1>Main Title</h1>
                <p>This is a <strong>test</strong> paragraph with <em>emphasis</em>.</p>
                <h2>Subtitle</h2>
                <p>Another paragraph with a <a href="https://example.com">link</a>.</p>
                <ul>
                    <li>List item 1</li>
                    <li>List item 2</li>
                </ul>
                <blockquote>This is a quote</blockquote>
                <pre><code>print("Hello World")</code></pre>
            </article>
        </main>
        <footer>Footer</footer>
        <script>console.log("ads")</script>
    </body>
    </html>
    """
    
    processor = ContentProcessor()
    result = processor.process_html_to_markdown(test_html, "https://example.com")
    
    print(f"✅ Processing successful: {result['success']}")
    print(f"📊 Original size: {result['original_size']} chars")
    print(f"📊 Processed size: {result['processed_size']} chars")
    print(f"📊 Reduction: {result['reduction_ratio']:.1%}")
    print(f"📄 Title: {result['metadata'].get('title', 'N/A')}")
    print(f"📄 Description: {result['metadata'].get('description', 'N/A')}")
    
    print("\n📝 Processed Content:")
    print("-" * 30)
    print(result['content'])
    
    print(f"\n📈 Processor Stats:")
    stats = processor.get_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_content_processor()