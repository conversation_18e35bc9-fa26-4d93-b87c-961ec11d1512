"""
Jina-inspired web crawler with advanced snapshot capabilities.
Based on <PERSON><PERSON>'s Puppeteer implementation with Python adaptations.
"""

import asyncio
import time
import json
import hashlib
from typing import Optional, Dict, Any, List, AsyncGenerator
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, asdict
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>, Page, TimeoutError as PlaywrightTimeoutError
from bs4 import BeautifulSoup
from bs4.element import Comment

from utils.config import config
from utils.logging import LoggerMixin, log_performance, log_error, get_logger

# Constants
DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# Module logger
logger = get_logger(__name__)


@dataclass
class PageSnapshot:
    """Represents a comprehensive snapshot of a web page (Jina-style)"""
    title: str = ""
    description: str = ""
    href: str = ""
    html: str = ""
    text: str = ""
    status: Optional[int] = None
    status_text: str = ""
    parsed: Optional[Dict] = None
    screenshot: Optional[bytes] = None
    imgs: List[Dict] = None
    pdfs: List[str] = None
    max_elem_depth: int = 0
    elem_count: int = 0
    is_intermediate: bool = False
    is_from_cache: bool = False
    last_mutation_idle: int = 0
    last_content_resource_loaded: Optional[int] = None
    last_media_resource_loaded: Optional[int] = None
    
    def __post_init__(self):
        if self.imgs is None:
            self.imgs = []
        if self.pdfs is None:
            self.pdfs = []


def clean_html_with_soup(html_content: str) -> str:
    """
    Cleans HTML content using BeautifulSoup.
    - Removes script, style, and other non-content tags.
    - Keeps the main structure and content.
    """
    if not html_content:
        return ""
    
    soup = BeautifulSoup(html_content, 'lxml')
    
    # Remove unwanted tags
    for tag in soup(["script", "style", "nav", "footer", "aside"]):
        tag.decompose()
        
    # Optional: Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()
        
    # Get cleaned HTML
    cleaned_html = str(soup)
    
    return cleaned_html


class RequestController:
    """Controls request concurrency and tracks resource loading"""
    
    def __init__(self, concurrency: int = 32):
        self.concurrency = concurrency
        self.active_requests = set()
        self.last_resource_loaded_at = 0
        self.last_content_resource_loaded_at = 0
        self.last_media_resource_loaded_at = 0
        
    async def on_request_start(self, request):
        """Handle request start"""
        self.active_requests.add(request)
        
    async def on_request_finish(self, request):
        """Handle request finish"""
        self.active_requests.discard(request)
        now = int(time.time() * 1000)
        self.last_resource_loaded_at = now
        
        # Categorize resource types
        resource_type = getattr(request, 'resource_type', '')
        if resource_type in ['document', 'script', 'xhr', 'fetch', 'prefetch', 'eventsource', 'websocket']:
            self.last_content_resource_loaded_at = now
        elif resource_type in ['stylesheet', 'image', 'font', 'media']:
            self.last_media_resource_loaded_at = now
    
    async def setup_page(self, page):
        """Set up request monitoring for a page"""
        page.on('request', self.on_request_start)
        page.on('requestfinished', self.on_request_finish)
        page.on('requestfailed', self.on_request_finish)


class JinaStyleCrawler(LoggerMixin):
    """
    Advanced web crawler inspired by Jina's implementation.
    Features snapshot-based crawling with comprehensive content extraction.
    """
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self._is_initialized = False
        self.circuit_breaker_hosts = set()
        self.concurrent_requests_per_page = 32
        
        # Readability-inspired content extraction script
        self.content_extraction_script = """
        (() => {
            // Content extraction functions
            function extractReadableContent() {
                // Find main content areas
                const contentSelectors = [
                    'article', 'main', '[role="main"]', '.content', '.main-content',
                    '#content', '#main', '.post-content', '.entry-content', '.article-content'
                ];
                
                let mainContent = null;
                for (const selector of contentSelectors) {
                    const element = document.querySelector(selector);
                    if (element && element.innerText && element.innerText.length > 100) {
                        mainContent = element;
                        break;
                    }
                }
                
                // Fallback to body
                if (!mainContent) {
                    mainContent = document.body;
                }
                
                if (!mainContent) return null;
                
                const textContent = mainContent.innerText || mainContent.textContent || '';
                
                return {
                    title: document.title || '',
                    content: mainContent.innerHTML || '',
                    textContent: textContent,
                    length: textContent.length,
                    excerpt: textContent.substring(0, 200),
                    byline: document.querySelector('[rel="author"], .author, .byline')?.textContent || '',
                    dir: document.dir || 'ltr',
                    siteName: document.querySelector('meta[property="og:site_name"]')?.content || '',
                    lang: document.documentElement.lang || 'en',
                    publishedTime: document.querySelector('meta[property="article:published_time"]')?.content || 
                                  document.querySelector('time[datetime]')?.getAttribute('datetime') || ''
                };
            }
            
            function getPageAnalytics() {
                const walker = document.createTreeWalker(
                    document.documentElement,
                    NodeFilter.SHOW_ELEMENT,
                    {
                        acceptNode: function(node) {
                            // Skip SVG elements for performance
                            return node.nodeName.toLowerCase() === 'svg' ? 
                                NodeFilter.FILTER_REJECT : NodeFilter.FILTER_ACCEPT;
                        }
                    },
                    false
                );
                
                let maxDepth = 0;
                let currentDepth = 0;
                let elementCount = 0;
                
                while (walker.nextNode()) {
                    elementCount++;
                    // Calculate depth by counting parent elements
                    let depth = 0;
                    let parent = walker.currentNode.parentElement;
                    while (parent) {
                        depth++;
                        parent = parent.parentElement;
                    }
                    maxDepth = Math.max(maxDepth, depth);
                }
                
                return { maxDepth, elementCount };
            }
            
            function briefImages() {
                const images = Array.from(document.querySelectorAll('img[src], img[data-src]'));
                return images.map(img => {
                    let src = img.src;
                    if (src.startsWith('data:') && img.dataset.src && !img.dataset.src.startsWith('data:')) {
                        src = img.dataset.src;
                    }
                    
                    try {
                        src = new URL(src, document.baseURI).toString();
                    } catch (e) {
                        // Keep original src if URL construction fails
                    }
                    
                    return {
                        src: src,
                        loaded: img.complete,
                        width: img.width || 0,
                        height: img.height || 0,
                        naturalWidth: img.naturalWidth || 0,
                        naturalHeight: img.naturalHeight || 0,
                        alt: img.alt || img.title || ''
                    };
                });
            }
            
            function giveSnapshot() {
                const analytics = getPageAnalytics();
                const parsed = extractReadableContent();
                const imgs = briefImages();
                
                return {
                    title: document.title || '',
                    description: document.querySelector('meta[name="description"]')?.content || '',
                    href: document.location.href,
                    html: document.documentElement.outerHTML,
                    text: document.body?.innerText || document.body?.textContent || '',
                    parsed: parsed,
                    imgs: imgs,
                    max_elem_depth: analytics.maxDepth,
                    elem_count: analytics.elementCount,
                    last_mutation_idle: Date.now()
                };
            }
            
            // Mutation observer for dynamic content
            let mutationTimeout;
            const mutationObserver = new MutationObserver(() => {
                clearTimeout(mutationTimeout);
                mutationTimeout = setTimeout(() => {
                    document.dispatchEvent(new CustomEvent('mutationIdle'));
                }, 200);
            });
            
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    mutationObserver.observe(document.documentElement, {
                        childList: true,
                        subtree: true,
                        attributes: true
                    });
                    mutationTimeout = setTimeout(() => {
                        document.dispatchEvent(new CustomEvent('mutationIdle'));
                    }, 200);
                });
            } else {
                mutationObserver.observe(document.documentElement, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
                mutationTimeout = setTimeout(() => {
                    document.dispatchEvent(new CustomEvent('mutationIdle'));
                }, 200);
            }
            
            // Expose functions globally
            window.giveSnapshot = giveSnapshot;
            window.briefImages = briefImages;
            window.getPageAnalytics = getPageAnalytics;
            window.extractReadableContent = extractReadableContent;
        })();
        """
        
    async def initialize(self) -> None:
        """Initialize the crawler with browser setup"""
        if self._is_initialized:
            return
            
        self.logger.info("Initializing Jina-style crawler...")
        start_time = time.time()
        
        try:
            self.playwright = await async_playwright().start()
            
            # Launch browser with Jina-inspired settings
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                ]
            )
            
            # Create context with enhanced settings
            self.context = await self.browser.new_context(
                viewport={'width': 1024, 'height': 1024},
                user_agent=config.crawler.user_agent.replace('Headless', '').replace(
                    'Mozilla/5.0 (X11; Linux x86_64)', 
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                ),
                ignore_https_errors=True,
                java_script_enabled=True,
            )
            
            self.context.set_default_timeout(config.crawler.timeout)
            
            duration = time.time() - start_time
            log_performance(self.logger, "jina_crawler_initialization", duration)
            self.logger.info(f"Jina-style crawler initialized in {duration:.2f}s")
            self._is_initialized = True
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "jina_crawler_initialization"})
            await self.cleanup()
            raise
    
    async def scrap_url(self, url: str, options: Optional[Dict[str, Any]] = None) -> AsyncGenerator[PageSnapshot, None]:
        """
        Scrap URL with Jina-style snapshot generation.
        Yields intermediate and final snapshots.
        """
        if not self._is_initialized:
            await self.initialize()
        
        options = options or {}
        start_time = time.time()
        page = None
        request_controller = RequestController(self.concurrent_requests_per_page)
        
        try:
            self.logger.info(f"Scraping URL with Jina approach: {url}")
            
            # Create new page
            page = await self.context.new_page()
            
            # Set up request interception and monitoring
            await page.route('**/*', self._handle_request)
            
            # Track requests for resource loading timing
            page.on('request', request_controller.on_request_start)
            page.on('requestfinished', request_controller.on_request_finish)
            page.on('requestfailed', request_controller.on_request_finish)
            
            # Inject content extraction script
            await page.add_init_script(self.content_extraction_script)
            
            # Set up snapshot collection
            snapshots = []
            snapshot_event = asyncio.Event()
            
            async def collect_snapshot():
                try:
                    snapshot_data = await page.evaluate("window.giveSnapshot()")
                    
                    # Clean HTML with BeautifulSoup
                    if snapshot_data and 'html' in snapshot_data:
                        cleaned_html = clean_html_with_soup(snapshot_data['html'])
                        snapshot_data['html'] = cleaned_html
                        if snapshot_data.get('parsed'):
                            snapshot_data['parsed']['content'] = clean_html_with_soup(snapshot_data['parsed']['content'])

                    snapshot = PageSnapshot(**snapshot_data)
                    snapshot.status = None  # Will be set from response
                    snapshot.last_content_resource_loaded = request_controller.last_content_resource_loaded_at
                    snapshot.last_media_resource_loaded = request_controller.last_media_resource_loaded_at
                    snapshots.append(snapshot)
                    snapshot_event.set()
                except Exception as e:
                    self.logger.warning(f"Error collecting snapshot: {e}")
            
            # Set up mutation idle listener
            await page.evaluate("""
                document.addEventListener('mutationIdle', () => {
                    window.collectSnapshot && window.collectSnapshot();
                });
            """)
            
            # Expose snapshot collection function
            await page.expose_function('collectSnapshot', collect_snapshot)
            
            # Navigate to URL
            response = await page.goto(
                url,
                wait_until=options.get('wait_until', 'domcontentloaded'),
                timeout=options.get('timeout', config.crawler.timeout)
            )
            
            # Wait for initial content
            await page.wait_for_load_state('domcontentloaded')
            
            # Collect initial snapshot
            await collect_snapshot()
            if snapshots:
                snapshots[-1].status = response.status if response else None
                snapshots[-1].status_text = response.status_text if response else ""
                snapshots[-1].is_intermediate = True
                yield snapshots[-1]
            
            # Wait for additional content loading
            try:
                await asyncio.wait_for(snapshot_event.wait(), timeout=5.0)
                snapshot_event.clear()
                
                # Collect final snapshot
                await collect_snapshot()
                if snapshots:
                    final_snapshot = snapshots[-1]
                    final_snapshot.status = response.status if response else None
                    final_snapshot.status_text = response.status_text if response else ""
                    final_snapshot.is_intermediate = False
                    
                    # Take screenshot if requested
                    if options.get('screenshot', False):
                        try:
                            screenshot = await page.screenshot(full_page=False)
                            final_snapshot.screenshot = screenshot
                        except Exception as e:
                            self.logger.warning(f"Failed to take screenshot: {e}")
                    
                    yield final_snapshot
                    
            except asyncio.TimeoutError:
                # Timeout waiting for mutations, use last snapshot
                if snapshots:
                    final_snapshot = snapshots[-1]
                    final_snapshot.is_intermediate = False
                    yield final_snapshot
            
            duration = time.time() - start_time
            log_performance(
                self.logger,
                "jina_url_scrap",
                duration,
                url=url,
                snapshots_count=len(snapshots)
            )
            
        except Exception as e:
            error_msg = f"Error scraping {url}: {str(e)}"
            log_error(self.logger, e, {"operation": "jina_url_scrap", "url": url})
            
            # Yield error snapshot
            error_snapshot = PageSnapshot(
                href=url,
                title="Error",
                text=error_msg,
                status=500
            )
            yield error_snapshot
            
        finally:
            if page:
                await page.close()
    
    async def _handle_request(self, route):
        """Handle page requests with filtering and optimization"""
        request = route.request
        url = request.url
        resource_type = request.resource_type
        
        # Block certain resource types for performance
        if resource_type in ['image', 'media', 'font'] and not url.startswith('data:'):
            await route.abort()
            return
        
        # Block known tracking and ad domains
        blocked_domains = [
            'google-analytics.com', 'googletagmanager.com', 'facebook.com',
            'doubleclick.net', 'googlesyndication.com', 'amazon-adsystem.com'
        ]
        
        parsed_url = urlparse(url)
        if any(domain in parsed_url.netloc for domain in blocked_domains):
            await route.abort()
            return
        
        # Continue with request
        await route.continue_()
    
    def get_url_digest(self, url: str) -> str:
        """Generate URL digest for caching (Jina-style)"""
        normalized_url = url.lower()
        # Remove fragment unless it's a route (starts with #/)
        parsed = urlparse(normalized_url)
        if parsed.fragment and not parsed.fragment.startswith('/'):
            normalized_url = normalized_url.split('#')[0]
        
        return hashlib.md5(normalized_url.encode()).hexdigest()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the crawler"""
        try:
            if not self._is_initialized:
                return {
                    "status": "not_initialized",
                    "browser_connected": False
                }
            
            # Test with simple page
            page = await self.context.new_page()
            await page.goto("data:text/html,<html><body><h1>Health Check</h1></body></html>")
            title = await page.title()
            await page.close()
            
            return {
                "status": "healthy",
                "browser_connected": True,
                "test_successful": bool(title),
                "crawler_type": "jina_style"
            }
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "jina_health_check"})
            return {
                "status": "error",
                "browser_connected": False,
                "error": str(e),
                "crawler_type": "jina_style"
            }
    
    async def cleanup(self) -> None:
        """Clean up browser resources"""
        self.logger.info("Cleaning up Jina-style crawler resources...")
        
        try:
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self._is_initialized = False
            self.logger.info("Jina-style crawler cleanup completed")
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "jina_cleanup"})


# Global instance
jina_crawler_instance = JinaStyleCrawler()


async def jina_crawler(
    url: str,
    wait_for_selector: Optional[str] = None,
    timeout: int = 30000,
    include_images: bool = True,
    include_links: bool = True,
    user_agent: Optional[str] = None
) -> PageSnapshot:
    """
    Simplified wrapper for Jina-style crawling.
    Returns the final snapshot from the crawler.
    """
    options = {
        'timeout': timeout,
        'screenshot': include_images,
        'wait_until': 'domcontentloaded'
    }
    
    final_snapshot = None
    async for snapshot in jina_crawler_instance.scrap_url(url, options):
        final_snapshot = snapshot
        if not snapshot.is_intermediate:
            break
    
    if final_snapshot is None:
        # Return error snapshot if nothing was captured
        final_snapshot = PageSnapshot(
            href=url,
            title="Error",
            text="Failed to capture page snapshot",
            status=500
        )
    
    # Convert to expected format for API compatibility
    final_snapshot.content = final_snapshot.text
    final_snapshot.markdown = final_snapshot.parsed.get('content', '') if final_snapshot.parsed else ''
    final_snapshot.metadata = {
        'max_elem_depth': final_snapshot.max_elem_depth,
        'elem_count': final_snapshot.elem_count,
        'images': final_snapshot.imgs,
        'pdfs': final_snapshot.pdfs or []
    }
    
    return final_snapshot
@dataclass
class SearchResult:
    """Search result data structure."""
    success: bool
    results: List[Dict[str, Any]]
    total_found: int
    error: Optional[str] = None


async def jina_search_site(
    site_url: str,
    query: str,
    max_results: int = 10,
    timeout: int = 30000,
    user_agent: Optional[str] = None
) -> SearchResult:
    """
    Search within a specific site using Jina-style approach.
    
    Args:
        site_url: Base URL of the site to search
        query: Search query
        max_results: Maximum number of results to return
        timeout: Request timeout in milliseconds
        user_agent: Custom user agent string
        
    Returns:
        SearchResult with search results
    """
    browser = None
    try:
        logger.info(f"Starting site search: {query} on {site_url}")
        
        # Launch browser
        browser = await async_playwright().start()
        browser_instance = await browser.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        )
        
        page = await browser_instance.new_page(
            user_agent=user_agent or DEFAULT_USER_AGENT
        )
        
        # Set up request controller
        request_controller = RequestController()
        await request_controller.setup_page(page)
        
        # Try different search approaches
        search_results = []
        
        # Approach 1: Try site's internal search
        internal_results = await _try_internal_search(page, site_url, query, max_results, timeout)
        if internal_results:
            search_results.extend(internal_results)
        
        # Approach 2: Use Google site search as fallback
        if len(search_results) < max_results:
            google_results = await _try_google_site_search(page, site_url, query, max_results - len(search_results), timeout)
            if google_results:
                search_results.extend(google_results)
        
        await browser_instance.close()
        
        return SearchResult(
            success=True,
            results=search_results[:max_results],
            total_found=len(search_results)
        )
        
    except Exception as e:
        logger.error(f"Error searching site {site_url}: {str(e)}")
        return SearchResult(
            success=False,
            results=[],
            total_found=0,
            error=str(e)
        )
    finally:
        if browser:
            await browser.stop()


async def _try_internal_search(
    page: Page,
    site_url: str,
    query: str,
    max_results: int,
    timeout: int
) -> List[Dict[str, Any]]:
    """Try to use the site's internal search functionality."""
    try:
        # Navigate to the site
        await page.goto(site_url, timeout=timeout, wait_until='domcontentloaded')
        
        # Look for common search elements
        search_selectors = [
            'input[type="search"]',
            'input[name*="search"]',
            'input[id*="search"]',
            'input[class*="search"]',
            '.search-input',
            '#search-input',
            '[placeholder*="search" i]'
        ]
        
        search_input = None
        for selector in search_selectors:
            try:
                search_input = await page.wait_for_selector(selector, timeout=2000)
                if search_input:
                    break
            except:
                continue
        
        if not search_input:
            return []
        
        # Enter search query
        await search_input.fill(query)
        
        # Try to submit the search
        submit_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            '.search-button',
            '#search-button',
            '[class*="search"][class*="btn"]'
        ]
        
        submitted = False
        for selector in submit_selectors:
            try:
                submit_btn = await page.query_selector(selector)
                if submit_btn:
                    await submit_btn.click()
                    submitted = True
                    break
            except:
                continue
        
        if not submitted:
            # Try pressing Enter
            await search_input.press('Enter')
        
        # Wait for results
        await page.wait_for_load_state('domcontentloaded')
        await asyncio.sleep(2)  # Give time for dynamic content
        
        # Extract search results
        results = await page.evaluate("""
            () => {
                const results = [];
                
                // Common result selectors
                const resultSelectors = [
                    '.search-result',
                    '.result',
                    '[class*="search-result"]',
                    '[class*="result-item"]'
                ];
                
                for (const selector of resultSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        elements.forEach((el, index) => {
                            const titleEl = el.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"]');
                            const linkEl = el.querySelector('a');
                            const snippetEl = el.querySelector('.snippet, .description, .excerpt, p');
                            
                            if (titleEl || linkEl) {
                                results.push({
                                    title: titleEl ? titleEl.textContent.trim() : (linkEl ? linkEl.textContent.trim() : ''),
                                    url: linkEl ? linkEl.href : '',
                                    snippet: snippetEl ? snippetEl.textContent.trim() : '',
                                    rank: index + 1
                                });
                            }
                        });
                        break; // Use first matching selector
                    }
                }
                
                return results.slice(0, """ + str(max_results) + """);
            }
        """)
        
        return results
        
    except Exception as e:
        logger.debug(f"Internal search failed for {site_url}: {str(e)}")
        return []


async def _try_google_site_search(
    page: Page,
    site_url: str,
    query: str,
    max_results: int,
    timeout: int
) -> List[Dict[str, Any]]:
    """Use Google site search as fallback."""
    try:
        # Parse domain from site_url
        from urllib.parse import urlparse
        parsed = urlparse(site_url)
        domain = parsed.netloc
        
        # Construct Google search URL
        google_query = f"site:{domain} {query}"
        google_url = f"https://www.google.com/search?q={google_query.replace(' ', '+')}"
        
        await page.goto(google_url, timeout=timeout, wait_until='domcontentloaded')
        
        # Wait for results
        await page.wait_for_selector('#search', timeout=5000)
        
        # Extract Google search results
        results = await page.evaluate("""
            () => {
                const results = [];
                const resultElements = document.querySelectorAll('#search .g');
                
                resultElements.forEach((el, index) => {
                    const titleEl = el.querySelector('h3');
                    const linkEl = el.querySelector('a');
                    const snippetEl = el.querySelector('.VwiC3b, .s3v9rd, .st');
                    
                    if (titleEl && linkEl) {
                        results.push({
                            title: titleEl.textContent.trim(),
                            url: linkEl.href,
                            snippet: snippetEl ? snippetEl.textContent.trim() : '',
                            rank: index + 1,
                            source: 'google'
                        });
                    }
                });
                
                return results.slice(0, """ + str(max_results) + """);
            }
        """)
        
        return results
        
    except Exception as e:
        logger.debug(f"Google site search failed for {site_url}: {str(e)}")
        return []