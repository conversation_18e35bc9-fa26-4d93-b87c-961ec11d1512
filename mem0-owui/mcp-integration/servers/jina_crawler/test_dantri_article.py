#!/usr/bin/env python3
"""
Test Jina Crawler với một bài báo cụ thể từ dantri.vn
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

async def test_dantri_article():
    """Test crawler với một bài báo cụ thể từ dantri.vn"""
    print("🧪 Testing Jina Crawler với bài báo dantri.vn")
    print("=" * 70)
    
    crawler = JiniCrawler()
    
    try:
        # Initialize crawler
        print("🚀 Initializing crawler...")
        await crawler.initialize()
        
        # Test với một bài báo cụ thể (sử dụng URL tổng quát)
        test_url = "https://dantri.com.vn/kinh-doanh.htm"
        
        print(f"🕷️ Crawling: {test_url}")
        print("⏳ Please wait...")
        
        # Crawl and process với content length lớn hơn
        result = await crawler.crawl_and_process(test_url, max_content_length=20000)
        
        print("\n" + "="*70)
        print("📊 DETAILED RESULTS:")
        print("="*70)
        
        print(f"✅ Success: {result.success}")
        
        if result.success:
            print(f"📄 Title: {result.title}")
            print(f"🔗 URL: {result.url}")
            print(f"📏 Original content: {len(result.original_content or '')} chars")
            print(f"📏 Cleaned content: {len(result.cleaned_content or '')} chars")
            print(f"📏 Processed content: {len(result.processed_content or '')} chars")
            print(f"⏱️ Processing time: {result.processing_time:.2f}s")
            
            if result.metadata:
                print(f"\n🤖 AI Processing Details:")
                print(f"   Model: {result.metadata.get('model', 'N/A')}")
                print(f"   Task: {result.metadata.get('task_type', 'N/A')}")
                print(f"   Input length: {result.metadata.get('original_length', 0)}")
                print(f"   Output length: {result.metadata.get('output_length', 0)}")
                
                # Calculate reduction ratio
                if result.metadata.get('original_length', 0) > 0:
                    reduction = (1 - result.metadata.get('output_length', 0) / result.metadata.get('original_length', 1)) * 100
                    print(f"   Content reduction: {reduction:.1f}%")
            
            print("\n" + "="*70)
            print("📝 PROCESSED CONTENT (Vietnamese):")
            print("="*70)
            
            if result.processed_content:
                # Show more content for article
                preview_length = min(2000, len(result.processed_content))
                preview = result.processed_content[:preview_length]
                print(preview)
                
                if len(result.processed_content) > preview_length:
                    remaining = len(result.processed_content) - preview_length
                    print(f"\n... (và {remaining} ký tự nữa)")
                    
                # Show some statistics
                lines = result.processed_content.split('\n')
                non_empty_lines = [line for line in lines if line.strip()]
                print(f"\n📊 Content Statistics:")
                print(f"   Total lines: {len(lines)}")
                print(f"   Non-empty lines: {len(non_empty_lines)}")
                print(f"   Average line length: {len(result.processed_content) / max(len(non_empty_lines), 1):.1f} chars")
                
            else:
                print("❌ No processed content")
                
        else:
            print(f"❌ Error: {result.error}")
            if result.original_content:
                print(f"📄 Original content preview: {result.original_content[:500]}...")
        
        # Test với URL khác
        print("\n" + "="*70)
        print("🔄 Testing với URL khác...")
        print("="*70)
        
        test_url2 = "https://dantri.com.vn/suc-khoe.htm"
        print(f"🕷️ Crawling: {test_url2}")
        
        result2 = await crawler.crawl_and_process(test_url2, max_content_length=15000)
        
        if result2.success:
            print(f"✅ Success: {result2.title}")
            print(f"📏 Content: {len(result2.processed_content or '')} chars")
            print(f"⏱️ Time: {result2.processing_time:.2f}s")
            
            if result2.processed_content:
                preview = result2.processed_content[:500]
                print(f"📝 Preview: {preview}...")
        else:
            print(f"❌ Failed: {result2.error}")
        
        # Health check
        print("\n" + "="*70)
        print("🏥 SYSTEM HEALTH CHECK:")
        print("="*70)
        
        health = await crawler.health_check()
        print(f"Overall Status: {health.get('status', 'unknown')}")
        
        if 'components' in health:
            for component, status in health['components'].items():
                component_status = status.get('status', 'unknown')
                print(f"  📦 {component}: {component_status}")
                
                if component == 'gemini_processor' and 'test_processing_time' in status:
                    print(f"     ⏱️ Test processing time: {status['test_processing_time']:.3f}s")
                if component == 'jina_crawler' and 'test_successful' in status:
                    print(f"     ✅ Test successful: {status['test_successful']}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up resources...")
        await crawler.cleanup()
        print("✅ Test hoàn thành!")

if __name__ == "__main__":
    asyncio.run(test_dantri_article())