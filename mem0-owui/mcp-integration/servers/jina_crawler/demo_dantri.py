#!/usr/bin/env python3
"""
Demo Jina Crawler MCP Server với dantri.vn
Showcase khả năng crawl và xử lý nội dung tiếng Việt
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

async def demo_dantri_crawler():
    """Demo crawler với các trang dantri.vn khác nhau"""
    print("🎯 DEMO: Jina Crawler MCP Server với dantri.vn")
    print("=" * 70)
    
    crawler = JiniCrawler()
    
    try:
        # Initialize
        print("🚀 Khởi tạo Jina Crawler...")
        await crawler.initialize()
        print("✅ Crawler đã sẵn sàng!")
        
        # Test URLs từ dantri.vn
        test_urls = [
            "https://dantri.com.vn/the-gioi.htm",
            "https://dantri.com.vn/kinh-doanh.htm", 
            "https://dantri.com.vn/suc-khoe.htm"
        ]
        
        print(f"\n🕷️ Sẽ crawl {len(test_urls)} trang từ dantri.vn:")
        for i, url in enumerate(test_urls, 1):
            print(f"   {i}. {url}")
        
        print(f"\n⏳ Bắt đầu crawling...")
        
        # Process each URL
        for i, url in enumerate(test_urls, 1):
            print(f"\n" + "="*70)
            print(f"📄 TEST {i}: {url}")
            print("="*70)
            
            result = await crawler.crawl_and_process(url, max_content_length=12000)
            
            if result.success:
                print(f"✅ Thành công!")
                print(f"📄 Tiêu đề: {result.title}")
                print(f"📏 Độ dài nội dung: {len(result.processed_content or '')} ký tự")
                print(f"⏱️ Thời gian xử lý: {result.processing_time:.2f}s")
                
                if result.metadata:
                    print(f"🤖 Model: {result.metadata.get('model', 'N/A')}")
                    print(f"📊 Input: {result.metadata.get('original_length', 0)} → Output: {result.metadata.get('output_length', 0)} chars")
                    if result.metadata.get('original_length', 0) > 0:
                        reduction = (1 - result.metadata.get('output_length', 0) / result.metadata.get('original_length', 1)) * 100
                        print(f"📉 Giảm kích thước: {reduction:.1f}%")
                
                if result.processed_content:
                    print(f"\n📝 Nội dung đã xử lý (200 ký tự đầu):")
                    print("-" * 50)
                    preview = result.processed_content[:200]
                    print(preview)
                    if len(result.processed_content) > 200:
                        print(f"... (và {len(result.processed_content) - 200} ký tự nữa)")
                    print("-" * 50)
            else:
                print(f"❌ Thất bại: {result.error}")
            
            # Small delay between requests
            if i < len(test_urls):
                print(f"\n⏳ Chờ 2 giây trước khi crawl trang tiếp theo...")
                await asyncio.sleep(2)
        
        # Summary
        print(f"\n" + "="*70)
        print("📊 TỔNG KẾT DEMO")
        print("="*70)
        
        # Health check
        health = await crawler.health_check()
        print(f"🏥 Trạng thái hệ thống: {health.get('status', 'unknown')}")
        
        if 'components' in health:
            for component, status in health['components'].items():
                component_status = status.get('status', 'unknown')
                print(f"   📦 {component}: {component_status}")
        
        print(f"\n✨ Tính năng nổi bật:")
        print(f"   🇻🇳 Tối ưu cho nội dung tiếng Việt")
        print(f"   🚀 Xử lý nhanh với Gemini 2.5 Flash Lite")
        print(f"   🧹 Làm sạch HTML với BeautifulSoup")
        print(f"   📱 Playwright crawler với snapshot")
        print(f"   🔄 Retry logic và error handling")
        print(f"   📊 Performance monitoring")
        
        print(f"\n🎯 MCP Tools có sẵn:")
        print(f"   • crawl_url - Crawl 1 URL")
        print(f"   • crawl_batch - Crawl nhiều URL đồng thời")
        print(f"   • search_site - Tìm kiếm trong website")
        print(f"   • health_check - Kiểm tra sức khỏe")
        print(f"   • get_crawler_stats - Thống kê hiệu suất")
        
    except Exception as e:
        print(f"❌ Demo thất bại: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print(f"\n🧹 Dọn dẹp tài nguyên...")
        await crawler.cleanup()
        print(f"✅ Demo hoàn thành!")
        print(f"\n🎉 Jina Crawler MCP Server sẵn sàng tích hợp với Open WebUI!")

if __name__ == "__main__":
    asyncio.run(demo_dantri_crawler())