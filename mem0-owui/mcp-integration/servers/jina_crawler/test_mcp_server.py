#!/usr/bin/env python3
"""
Test script for Jina Crawler MCP Server
"""

import asyncio
import json
import subprocess
import sys
from typing import Dict, Any

async def test_mcp_server():
    """Test MCP server functionality"""
    print("🧪 Testing Jina Crawler MCP Server")
    print("=" * 60)
    
    # Start MCP server process
    process = subprocess.Popen(
        [sys.executable, "mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        cwd="."
    )
    
    try:
        # Test 1: Initialize
        print("📡 Test 1: Server Initialization")
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        process.stdin.write(json.dumps(init_message) + "\n")
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ Initialization response: {response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
        
        # Test 2: List tools
        print("\n🔧 Test 2: List Tools")
        list_tools_message = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        process.stdin.write(json.dumps(list_tools_message) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            tools = response.get('result', {}).get('tools', [])
            print(f"✅ Found {len(tools)} tools:")
            for tool in tools:
                print(f"   - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
        
        # Test 3: Health check tool
        print("\n🏥 Test 3: Health Check Tool")
        health_check_message = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "health_check",
                "arguments": {}
            }
        }
        
        process.stdin.write(json.dumps(health_check_message) + "\n")
        process.stdin.flush()
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            result = response.get('result', {})
            if 'content' in result and result['content']:
                content = json.loads(result['content'][0]['text'])
                print(f"✅ Health check status: {content.get('health_check', {}).get('status', 'Unknown')}")
        
        # Test 4: Crawl URL tool (quick test)
        print("\n🕷️ Test 4: Crawl URL Tool")
        crawl_message = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "crawl_url",
                "arguments": {
                    "url": "https://httpbin.org/html",
                    "max_content_length": 5000
                }
            }
        }
        
        process.stdin.write(json.dumps(crawl_message) + "\n")
        process.stdin.flush()
        
        # Wait a bit longer for crawling
        await asyncio.sleep(5)
        
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            result = response.get('result', {})
            if 'content' in result and result['content']:
                content = json.loads(result['content'][0]['text'])
                success = content.get('success', False)
                title = content.get('title', 'No title')
                processing_time = content.get('processing_time', 0)
                print(f"✅ Crawl result: Success={success}, Title='{title}', Time={processing_time:.2f}s")
        
        print("\n✅ MCP Server tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        
    finally:
        # Cleanup
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()

if __name__ == "__main__":
    asyncio.run(test_mcp_server())