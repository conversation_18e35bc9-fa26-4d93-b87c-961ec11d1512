#!/usr/bin/env python3
"""
Test Jina Crawler với dantri.vn
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

async def test_dantri_crawler():
    """Test crawler với dantri.vn"""
    print("🧪 Testing Jina Crawler với dantri.vn")
    print("=" * 60)
    
    crawler = JiniCrawler()
    
    try:
        # Initialize crawler
        print("🚀 Initializing crawler...")
        await crawler.initialize()
        
        # Test URL từ dantri.vn
        test_url = "https://dantri.com.vn/the-gioi.htm"
        
        print(f"🕷️ Crawling: {test_url}")
        print("⏳ Please wait...")
        
        # Crawl and process
        result = await crawler.crawl_and_process(test_url, max_content_length=15000)
        
        print("\n" + "="*60)
        print("📊 RESULTS:")
        print("="*60)
        
        print(f"✅ Success: {result.success}")
        
        if result.success:
            print(f"📄 Title: {result.title}")
            print(f"🔗 URL: {result.url}")
            print(f"📏 Original content length: {len(result.original_content or '')} chars")
            print(f"📏 Processed content length: {len(result.processed_content or '')} chars")
            print(f"⏱️ Processing time: {result.processing_time:.2f}s")
            
            if result.metadata:
                print(f"🤖 Model: {result.metadata.get('model', 'N/A')}")
                print(f"📊 Task type: {result.metadata.get('task_type', 'N/A')}")
                print(f"📈 Original length: {result.metadata.get('original_length', 0)}")
                print(f"📉 Output length: {result.metadata.get('output_length', 0)}")
            
            print("\n" + "="*60)
            print("📝 PROCESSED CONTENT PREVIEW:")
            print("="*60)
            
            if result.processed_content:
                # Show first 1000 characters
                preview = result.processed_content[:1000]
                print(preview)
                if len(result.processed_content) > 1000:
                    print(f"\n... (và {len(result.processed_content) - 1000} ký tự nữa)")
            else:
                print("❌ No processed content")
                
        else:
            print(f"❌ Error: {result.error}")
            if result.original_content:
                print(f"📄 Original content preview: {result.original_content[:200]}...")
        
        # Health check
        print("\n" + "="*60)
        print("🏥 HEALTH CHECK:")
        print("="*60)
        
        health = await crawler.health_check()
        print(f"Status: {health.get('status', 'unknown')}")
        
        if 'components' in health:
            for component, status in health['components'].items():
                print(f"  {component}: {status.get('status', 'unknown')}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        await crawler.cleanup()
        print("✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(test_dantri_crawler())