#!/usr/bin/env python3
"""
Test script for pandas MCP server
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the pandas_mcp directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "pandas_mcp"))

def test_pandas_mcp_imports():
    """Test if pandas MCP modules can be imported."""
    try:
        from core.metadata import read_metadata
        from core.execution import run_pandas_code
        from core.visualization import generate_chartjs
        print("✅ All pandas MCP modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import pandas MCP modules: {e}")
        return False

def test_metadata_function():
    """Test metadata reading function."""
    try:
        from core.metadata import read_metadata
        
        # Create a test CSV file
        test_csv = Path(__file__).parent / "test_data.csv"
        test_csv.write_text("Name,Age,City\nJohn,25,New York\nJane,30,Los Angeles\nBob,35,Chicago")
        
        result = read_metadata(str(test_csv))
        print(f"✅ Metadata test successful: {result.get('status', 'UNKNOWN')}")
        
        # Clean up
        test_csv.unlink()
        return True
    except Exception as e:
        print(f"❌ Metadata test failed: {e}")
        return False

def test_pandas_execution():
    """Test pandas code execution."""
    try:
        from core.execution import run_pandas_code
        
        test_code = """
import pandas as pd
import numpy as np

# Create test data
data = {
    'Name': ['John', 'Jane', 'Bob'],
    'Age': [25, 30, 35],
    'City': ['New York', 'Los Angeles', 'Chicago']
}
df = pd.DataFrame(data)

# Basic operations
result = {
    'shape': df.shape,
    'columns': df.columns.tolist(),
    'head': df.head().to_dict('records')
}
result
"""
        
        result = run_pandas_code(test_code)
        print(f"✅ Pandas execution test successful: {result.get('status', 'UNKNOWN')}")
        return True
    except Exception as e:
        print(f"❌ Pandas execution test failed: {e}")
        return False

def test_chart_generation():
    """Test chart generation function."""
    try:
        from core.visualization import generate_chartjs
        
        test_data = {
            "columns": [
                {
                    "name": "Category",
                    "type": "string",
                    "examples": ["A", "B", "C"]
                },
                {
                    "name": "Value",
                    "type": "number",
                    "examples": [10, 20, 30]
                }
            ]
        }
        
        result = generate_chartjs(test_data, ["bar"], "Test Chart")
        print(f"✅ Chart generation test successful: {result.get('status', 'UNKNOWN')}")
        return True
    except Exception as e:
        print(f"❌ Chart generation test failed: {e}")
        return False

async def test_mcp_server():
    """Test MCP server functionality."""
    try:
        from server import server, handle_list_tools, handle_call_tool
        
        # Test listing tools
        tools_result = await handle_list_tools()
        print(f"✅ MCP server tools listed: {len(tools_result.tools)} tools")
        
        # Test tool calls
        test_args = {
            "file_path": str(Path(__file__).parent / "test_data.csv")
        }
        
        # Create test file
        test_csv = Path(__file__).parent / "test_data.csv"
        test_csv.write_text("Name,Age,City\nJohn,25,New York\nJane,30,Los Angeles")
        
        try:
            result = await handle_call_tool("read_metadata", test_args)
            print("✅ MCP server tool call successful")
        finally:
            # Clean up
            if test_csv.exists():
                test_csv.unlink()
        
        return True
    except Exception as e:
        print(f"❌ MCP server test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Pandas MCP Server...")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_pandas_mcp_imports),
        ("Metadata Function", test_metadata_function),
        ("Pandas Execution", test_pandas_execution),
        ("Chart Generation", test_chart_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pandas MCP server is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 