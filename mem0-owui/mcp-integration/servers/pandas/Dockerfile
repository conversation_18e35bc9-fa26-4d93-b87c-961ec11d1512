FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy pandas MCP server
COPY pandas_mcp/ ./pandas_mcp/
COPY server.py .

# Create necessary directories
RUN mkdir -p logs charts

# Expose port (if needed)
EXPOSE 8000

# Run the server
CMD ["python3", "server.py"]
