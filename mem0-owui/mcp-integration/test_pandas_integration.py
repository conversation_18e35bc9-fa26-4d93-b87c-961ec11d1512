#!/usr/bin/env python3
"""
Integration test for pandas MCP server
"""

import asyncio
import json
import sys
from pathlib import Path

# Add pandas server to path
sys.path.insert(0, str(Path(__file__).parent / "servers" / "pandas"))

async def test_pandas_mcp_integration():
    """Test pandas MCP server integration."""
    try:
        from servers.pandas.server import server, handle_list_tools, handle_call_tool
        
        print("🧪 Testing pandas MCP server integration...")
        
        # Test listing tools
        tools_result = await handle_list_tools()
        print(f"✅ Found {len(tools_result.tools)} tools:")
        for tool in tools_result.tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # Test metadata tool
        test_csv = Path(__file__).parent / "servers" / "pandas" / "test_data" / "sample_data.csv"
        if test_csv.exists():
            result = await handle_call_tool("read_metadata", {"file_path": str(test_csv)})
            print("✅ Metadata tool test successful")
        
        # Test pandas execution tool
        test_code = """
import pandas as pd
import numpy as np

# Create test data
data = {
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [25, 30, 35],
    'City': ['New York', 'Los Angeles', 'Chicago']
}
df = pd.DataFrame(data)

# Basic operations
result = {
    'shape': df.shape,
    'columns': df.columns.tolist(),
    'head': df.head().to_dict('records')
}
result
"""
        
        result = await handle_call_tool("run_pandas_code", {"code": test_code})
        print("✅ Pandas execution tool test successful")
        
        # Test chart generation tool
        test_data = {
            "columns": [
                {
                    "name": "Category",
                    "type": "string",
                    "examples": ["A", "B", "C"]
                },
                {
                    "name": "Value",
                    "type": "number",
                    "examples": [10, 20, 30]
                }
            ]
        }
        
        result = await handle_call_tool("generate_chartjs", {
            "data": test_data,
            "chart_types": ["bar"],
            "title": "Test Chart"
        })
        print("✅ Chart generation tool test successful")
        
        print("🎉 All integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_pandas_mcp_integration())
    sys.exit(0 if success else 1)
