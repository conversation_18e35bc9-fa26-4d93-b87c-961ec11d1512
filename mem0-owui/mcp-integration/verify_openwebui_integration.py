#!/usr/bin/env python3
"""
Verify MCPO server is ready for Open WebUI OpenAPI Server integration
"""

import requests
import json
import sys
from typing import Dict, List, Any

class MCPOVerifier:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.api_key = "acca-enhanced-rag-mcp-key-2025"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.results = []
    
    def log_result(self, test_name: str, success: bool, message: str, details: Any = None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details
        })
        
        if details and not success:
            print(f"   Details: {details}")
    
    def test_server_connectivity(self) -> bool:
        """Test if MCPO server is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code in [200, 404]:  # 404 is OK, means server is running
                self.log_result("Server Connectivity", True, "MCPO server is running")
                return True
            else:
                self.log_result("Server Connectivity", False, f"Unexpected status code: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_result("Server Connectivity", False, "Cannot connect to MCPO server")
            return False
        except Exception as e:
            self.log_result("Server Connectivity", False, f"Connection error: {str(e)}")
            return False
    
    def test_openapi_spec(self) -> bool:
        """Test if OpenAPI specification is available"""
        try:
            response = requests.get(f"{self.base_url}/openapi.json", headers=self.headers, timeout=10)
            if response.status_code == 200:
                spec = response.json()
                
                # Check required OpenAPI fields
                required_fields = ['openapi', 'info', 'paths']
                missing_fields = [field for field in required_fields if field not in spec]
                
                if missing_fields:
                    self.log_result("OpenAPI Spec", False, f"Missing required fields: {missing_fields}")
                    return False
                
                # Check info section
                info = spec.get('info', {})
                if 'title' not in info or 'version' not in info:
                    self.log_result("OpenAPI Spec", False, "Missing title or version in info section")
                    return False
                
                self.log_result("OpenAPI Spec", True, f"Valid OpenAPI spec: {info.get('title')} v{info.get('version')}")
                return True
            else:
                self.log_result("OpenAPI Spec", False, f"HTTP {response.status_code}: {response.text}")
                return False
        except Exception as e:
            self.log_result("OpenAPI Spec", False, f"Error fetching OpenAPI spec: {str(e)}")
            return False
    
    def test_authentication(self) -> bool:
        """Test if authentication is working"""
        # Test with correct API key
        try:
            response = requests.get(f"{self.base_url}/openapi.json", headers=self.headers, timeout=10)
            if response.status_code == 200:
                self.log_result("Authentication (Valid Key)", True, "API key authentication working")
            else:
                self.log_result("Authentication (Valid Key)", False, f"Failed with valid key: HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Authentication (Valid Key)", False, f"Error with valid key: {str(e)}")
            return False
        
        # Test with invalid API key
        try:
            invalid_headers = {"Authorization": "Bearer invalid-key", "Content-Type": "application/json"}
            response = requests.get(f"{self.base_url}/openapi.json", headers=invalid_headers, timeout=10)
            if response.status_code == 401:
                self.log_result("Authentication (Invalid Key)", True, "Properly rejects invalid API key")
            else:
                self.log_result("Authentication (Invalid Key)", False, f"Should reject invalid key but got HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Authentication (Invalid Key)", False, f"Error testing invalid key: {str(e)}")
            return False
        
        return True
    
    def test_available_tools(self) -> bool:
        """Test if MCP tools are available"""
        try:
            response = requests.get(f"{self.base_url}/openapi.json", headers=self.headers, timeout=10)
            if response.status_code != 200:
                self.log_result("Available Tools", False, "Cannot fetch OpenAPI spec")
                return False
            
            spec = response.json()
            description = spec.get('info', {}).get('description', '')
            
            # Check for expected servers in description
            original_servers = ['document_processing', 'vietnamese_language', 'web_automation', 'time_utilities', 'weather_service']
            new_servers = ['filesystem', 'wikipedia', 'sqlite', 'github', 'brave_search']
            expected_servers = original_servers + new_servers
            found_servers = []
            
            for server in expected_servers:
                if server in description:
                    found_servers.append(server)
            
            if len(found_servers) == len(expected_servers):
                self.log_result("Available Tools", True, f"All {len(expected_servers)} MCP servers available: {', '.join(sorted(found_servers))}")
                return True
            else:
                missing = set(expected_servers) - set(found_servers)
                self.log_result("Available Tools", False, f"Missing servers: {', '.join(sorted(missing))}")
                return False
                
        except Exception as e:
            self.log_result("Available Tools", False, f"Error checking tools: {str(e)}")
            return False
    
    def test_individual_server_specs(self) -> bool:
        """Test individual server OpenAPI specs"""
        original_servers = ['document_processing', 'vietnamese_language', 'web_automation', 'time_utilities', 'weather_service']
        new_servers = ['filesystem', 'wikipedia', 'sqlite', 'github', 'brave_search']
        all_servers = original_servers + new_servers
        all_success = True
        
        for server in all_servers:
            try:
                response = requests.get(f"{self.base_url}/{server}/openapi.json", headers=self.headers, timeout=10)
                if response.status_code == 200:
                    spec = response.json()
                    paths = spec.get('paths', {})
                    tool_count = len(paths)
                    server_type = "NEW" if server in new_servers else "ORIG"
                    self.log_result(f"Server Spec ({server}) [{server_type}]", True, f"{tool_count} tools available")
                else:
                    self.log_result(f"Server Spec ({server})", False, f"HTTP {response.status_code}")
                    all_success = False
            except Exception as e:
                self.log_result(f"Server Spec ({server})", False, f"Error: {str(e)}")
                all_success = False
        
        return all_success
    
    def test_sample_tool_call(self) -> bool:
        """Test a sample tool call"""
        try:
            # Test time utilities (simple tool)
            response = requests.post(
                f"{self.base_url}/time_utilities/get_current_time",
                headers=self.headers,
                json={"timezone": "UTC"},
                timeout=15
            )
            
            if response.status_code == 200:
                self.log_result("Sample Tool Call", True, "Time utilities tool working")
                return True
            else:
                self.log_result("Sample Tool Call", False, f"HTTP {response.status_code}: {response.text}")
                return False
        except Exception as e:
            self.log_result("Sample Tool Call", False, f"Error calling tool: {str(e)}")
            return False
    
    def test_cors_headers(self) -> bool:
        """Test CORS headers for Open WebUI integration"""
        try:
            response = requests.options(f"{self.base_url}/openapi.json", headers=self.headers, timeout=10)
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
            }
            
            if cors_headers['Access-Control-Allow-Origin']:
                self.log_result("CORS Headers", True, f"CORS enabled: {cors_headers['Access-Control-Allow-Origin']}")
                return True
            else:
                self.log_result("CORS Headers", False, "CORS headers not found")
                return False
        except Exception as e:
            self.log_result("CORS Headers", False, f"Error checking CORS: {str(e)}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all verification tests"""
        print("🔍 Verifying MCPO server for Open WebUI integration...\n")
        
        tests = [
            self.test_server_connectivity,
            self.test_openapi_spec,
            self.test_authentication,
            self.test_available_tools,
            self.test_individual_server_specs,
            self.test_sample_tool_call,
            self.test_cors_headers
        ]
        
        all_passed = True
        for test in tests:
            success = test()
            if not success:
                all_passed = False
            print()  # Add spacing between tests
        
        return all_passed
    
    def print_summary(self):
        """Print test summary"""
        passed = sum(1 for result in self.results if result['success'])
        total = len(self.results)
        
        print("=" * 60)
        print(f"📊 VERIFICATION SUMMARY: {passed}/{total} tests passed")
        print("=" * 60)
        
        if passed == total:
            print("🎉 SUCCESS: MCPO server is ready for Open WebUI integration!")
            print("\n📋 Next steps:")
            print("1. Go to Open WebUI Admin Settings > Tools > OpenAPI Servers")
            print("2. Add server with URL: http://localhost:8000")
            print("3. Set Authorization header: Bearer acca-enhanced-rag-mcp-key-2025")
            print("4. Test tools in Open WebUI chat")
        else:
            print("❌ FAILED: Some tests failed. Please fix issues before integration.")
            print("\n🔧 Failed tests:")
            for result in self.results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['message']}")
        
        print("\n🔗 Integration Guide: mcp-integration/OPENWEBUI_OPENAPI_INTEGRATION.md")

def main():
    verifier = MCPOVerifier()
    success = verifier.run_all_tests()
    verifier.print_summary()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()