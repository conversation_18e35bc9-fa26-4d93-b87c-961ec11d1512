#!/usr/bin/env python3
"""
Test script for pandas MCP integration with Open WebUI
Verifies that the pandas MCP server is properly integrated and accessible through MCPO
"""

import asyncio
import json
import tempfile
import os
import pandas as pd
from pathlib import Path

async def test_pandas_mcp_integration():
    """Test the pandas MCP integration"""
    print("🧪 Testing pandas MCP integration with Open WebUI...")
    
    # Test 1: Check if MCPO is running
    print("\n1️⃣ Checking MCPO server status...")
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:3001/openapi.json", timeout=5.0)
            if response.status_code == 200:
                print("✅ MCPO server is running")
                print(f"   Version: {response.json().get('info', {}).get('version', 'unknown')}")
            else:
                print(f"❌ MCPO server returned status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to connect to MCPO server: {e}")
        return False
    
    # Test 2: Check if pandas MCP server is available
    print("\n2️⃣ Checking pandas MCP server availability...")
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:3001/pandas/openapi.json", timeout=5.0)
            if response.status_code == 200:
                print("✅ pandas MCP server is available through MCPO")
            else:
                print(f"❌ pandas MCP server returned status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to access pandas MCP server: {e}")
        return False
    
    # Test 3: Create test data
    print("\n3️⃣ Creating test data...")
    try:
        # Create a sample CSV file
        test_data = {
            'Name': ['Alice', 'Bob', 'Charlie', 'Diana'],
            'Age': [25, 30, 35, 28],
            'City': ['New York', 'London', 'Tokyo', 'Paris'],
            'Salary': [50000, 60000, 70000, 55000]
        }
        
        df = pd.DataFrame(test_data)
        
        # Save to temporary CSV file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            test_file_path = f.name
        
        print("✅ Test CSV file created")
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        return False
    
    # Test 4: Test read_metadata functionality
    print("\n4️⃣ Testing read_metadata functionality...")
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:3001/pandas/read_metadata",
                json={"file_path": test_file_path},
                timeout=30.0
            )
            if response.status_code == 200:
                metadata = response.json()
                if metadata.get("status") == "SUCCESS":
                    print("✅ read_metadata working correctly")
                    print(f"   File type: {metadata.get('file_info', {}).get('type')}")
                    print(f"   Rows: {metadata.get('data', {}).get('rows')}")
                    print(f"   Columns: {len(metadata.get('data', {}).get('columns', []))}")
                else:
                    print(f"❌ read_metadata failed: {metadata.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ read_metadata returned status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to test read_metadata: {e}")
        return False
    
    # Test 5: Test run_pandas_code functionality
    print("\n5️⃣ Testing run_pandas_code functionality...")
    try:
        code = f"""
import pandas as pd
df = pd.read_csv('{test_file_path}')
result = df.groupby('City')['Salary'].mean().to_dict()
        """
        
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:3001/pandas/run_pandas_code",
                json={"code": code},
                timeout=30.0
            )
            if response.status_code == 200:
                result = response.json()
                if not result.get("isError", False):
                    print("✅ run_pandas_code working correctly")
                    print(f"   Result preview: {str(result.get('content', [])[:100])}...")
                else:
                    print(f"❌ run_pandas_code failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ run_pandas_code returned status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to test run_pandas_code: {e}")
        return False
    
    # Test 6: Test generate_chartjs functionality
    print("\n6️⃣ Testing generate_chartjs functionality...")
    try:
        chart_data = {
            "columns": [
                {
                    "name": "City",
                    "type": "string",
                    "examples": ["New York", "London", "Tokyo", "Paris"]
                },
                {
                    "name": "Average Salary",
                    "type": "number",
                    "examples": [50000, 60000, 70000, 55000]
                }
            ]
        }
        
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:3001/pandas/generate_chartjs",
                json={
                    "data": chart_data,
                    "chart_types": ["bar"],
                    "title": "Average Salary by City"
                },
                timeout=30.0
            )
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "SUCCESS":
                    print("✅ generate_chartjs working correctly")
                    print(f"   Chart type: {result.get('chart_type')}")
                else:
                    print(f"❌ generate_chartjs failed: {result.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ generate_chartjs returned status: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Failed to test generate_chartjs: {e}")
        return False
    
    # Clean up test file
    try:
        if 'test_file_path' in locals():
            os.unlink(test_file_path)
    except Exception as e:
        print(f"⚠️  Warning: Failed to clean up test file: {e}")
    
    print("\n🎉 All tests passed! pandas MCP integration is working correctly.")
    return True

if __name__ == "__main__":
    asyncio.run(test_pandas_mcp_integration())