{"mcpServers": {"document_processing": {"command": "python3", "args": ["/app/servers/document_processing/server.py"]}, "vietnamese_language": {"command": "python3", "args": ["/app/servers/vietnamese_language/server.py"]}, "web_automation": {"command": "python3", "args": ["/app/servers/web_automation/server_playwright_fixed.py"], "env": {"PLAYWRIGHT_BROWSERS_PATH": "/ms-playwright", "DISPLAY": "", "HEADLESS": "true"}}, "time_utilities": {"command": "python3", "args": ["/app/servers/time_utilities/server.py"]}, "weather_service": {"command": "python3", "args": ["/app/servers/weather_service/server.py"]}, "filesystem": {"command": "python3", "args": ["/app/servers/filesystem/server.py"]}, "wikipedia": {"command": "python3", "args": ["/app/servers/wikipedia/server.py"]}, "sqlite": {"command": "python3", "args": ["/app/servers/sqlite/server.py"]}, "github": {"command": "python3", "args": ["/app/servers/github/server.py"]}, "brave_search": {"command": "python3", "args": ["/app/servers/brave_search/server.py"]}, "gemini_search_engine": {"command": "python3", "args": ["/app/servers/gemini_search_engine/server.py"]}, "jina_crawler": {"command": "python3", "args": ["/app/servers/jina_crawler/server.py"]}, "pandas": {"command": "python3", "args": ["/app/servers/pandas/server.py"], "env": {"PYTHONPATH": "/app/servers/pandas_mcp"}}}}