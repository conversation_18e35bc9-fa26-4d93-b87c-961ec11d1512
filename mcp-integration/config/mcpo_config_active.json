{"mcpServers": {"document_processing": {"command": "python3", "args": ["/app/servers/document_processing/server.py"]}, "vietnamese_language": {"command": "python3", "args": ["/app/servers/vietnamese_language/server.py"]}, "web_automation_real_browser": {"command": "python3", "args": ["/home/<USER>/AccA/AccA/mcp-integration/servers/web_automation/server_real_browser_bypass.py"], "env": {"NODE_PATH": "/usr/local/lib/node_modules:/home/<USER>/AccA/AccA/mcp-integration/servers/web_automation/node_modules", "DISPLAY": ":99", "XVFB_DISPLAY": ":99", "REAL_BROWSER": "true", "PYTHONPATH": "/home/<USER>/AccA/AccA"}}, "time_utilities": {"command": "python3", "args": ["/app/servers/time_utilities/server.py"]}, "weather_service": {"command": "python3", "args": ["/app/servers/weather_service/server.py"]}, "filesystem": {"command": "python3", "args": ["/app/servers/filesystem/server.py"]}, "wikipedia": {"command": "python3", "args": ["/app/servers/wikipedia/server.py"]}, "sqlite": {"command": "python3", "args": ["/app/servers/sqlite/server.py"]}, "github": {"command": "python3", "args": ["/app/servers/github/server.py"]}, "brave_search": {"command": "python3", "args": ["/app/servers/brave_search/server.py"]}, "gemini_search_engine": {"command": "python3", "args": ["/app/servers/gemini_search_engine/server.py"]}, "jina_crawler": {"command": "python3", "args": ["/app/servers/jina_crawler/server.py"]}, "pandas": {"command": "python3", "args": ["/app/servers/pandas/server.py"], "env": {"PYTHONPATH": "/app/servers/pandas_mcp"}}}}