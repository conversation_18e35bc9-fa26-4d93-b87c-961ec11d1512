FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install FastAPI, uvicorn, and pydantic
RUN pip install fastapi uvicorn pydantic

# Copy all files
COPY . .

# Expose port
EXPOSE 8008

# Run improved OpenAPI wrapper
CMD ["python", "improved_http_wrapper.py"]
