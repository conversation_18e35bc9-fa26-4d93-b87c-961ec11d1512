#!/usr/bin/env python3
"""
Direct test of Jina Crawler functionality
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

async def test_direct_crawler():
    """Test crawler directly"""
    print("🧪 Direct Test của Jina Crawler")
    print("=" * 60)
    
    crawler = JiniCrawler()
    
    try:
        # Initialize
        print("🚀 Initializing crawler...")
        await crawler.initialize()
        print("✅ Crawler initialized successfully")
        
        # Test với dantri.vn
        test_url = "https://dantri.com.vn"
        print(f"\n🕷️ Testing crawl: {test_url}")
        
        result = await crawler.crawl_and_process(test_url, max_content_length=8000)
        
        print(f"\n📊 RESULTS:")
        print(f"✅ Success: {result.success}")
        if result.success:
            print(f"📄 Title: {result.title}")
            print(f"📏 Content length: {len(result.processed_content or '')} chars")
            print(f"⏱️ Processing time: {result.processing_time:.2f}s")
            print(f"🤖 Model: {result.metadata.get('model', 'N/A') if result.metadata else 'N/A'}")
            
            if result.processed_content:
                preview = result.processed_content[:300]
                print(f"\n📝 Content preview:")
                print(f"{preview}...")
        else:
            print(f"❌ Error: {result.error}")
        
        # Health check
        print(f"\n🏥 Health check:")
        health = await crawler.health_check()
        print(f"Status: {health.get('status', 'unknown')}")
        
        # Test MCP server components
        print(f"\n🔧 Testing MCP server components...")
        from mcp_server import jina_server
        
        await jina_server.initialize()
        print("✅ MCP server initialized")
        
        # Test tools
        from mcp_server import server
        tools = await server._list_tools_handler()
        print(f"✅ Found {len(tools)} MCP tools:")
        for tool in tools:
            print(f"   - {tool.name}: {tool.description}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n🧹 Cleaning up...")
        await crawler.cleanup()
        print("✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(test_direct_crawler())