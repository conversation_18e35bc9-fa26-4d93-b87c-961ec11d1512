#!/usr/bin/env python3
"""
Cloudflare Resistant HTTP Wrapper for Jina Crawler
"""

import asyncio
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import cloudflare resistant crawler
from cloudflare_resistant_crawler import CloudflareResistantJiniCrawler
from paywall_bypass_crawler import PaywallBypassCrawler

# Pydantic models for request validation
class CrawlUrlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl and process", example="https://dantri.com.vn")
    max_content_length: Optional[int] = Field(
        20000, 
        description="Maximum content length to process (increased for better extraction)", 
        ge=1000, 
        le=50000
    )
    task_type: Optional[str] = Field(
        "news_extraction",
        description="Type of processing task",
        example="news_extraction"
    )

class CrawlBatchRequest(BaseModel):
    urls: list[str] = Field(..., description="List of URLs to crawl", example=["https://dantri.com.vn", "https://vnexpress.net"])
    max_content_length: Optional[int] = Field(
        20000, 
        description="Maximum content length per URL", 
        ge=1000, 
        le=50000
    )
    task_type: Optional[str] = Field(
        "news_extraction",
        description="Type of processing task",
        example="news_extraction"
    )

class EmptyRequest(BaseModel):
    pass

app = FastAPI(
    title="Cloudflare Resistant Jina Crawler Tools",
    description="HTTP wrapper for Jina Crawler with Cloudflare resistance",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instances
crawler = None
paywall_crawler = None

@app.on_event("startup")
async def startup_event():
    """Initialize crawler on startup"""
    global crawler, paywall_crawler
    crawler = CloudflareResistantJiniCrawler()
    paywall_crawler = PaywallBypassCrawler()
    await crawler.initialize()
    await paywall_crawler.initialize()
    print("🚀 Cloudflare Resistant Jina Crawler HTTP server started")
    print("🔓 Paywall bypass crawler initialized")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler, paywall_crawler
    if crawler:
        await crawler.cleanup()
    if paywall_crawler:
        await paywall_crawler.cleanup()
    print("✅ Cloudflare Resistant Jina Crawler HTTP server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Cloudflare Resistant Jina Crawler HTTP Server + Paywall Bypass", "tools": 9}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "cloudflare-resistant-jina-crawler"}

@app.post("/crawl_url", operation_id="crawl_url")
async def crawl_url(request: CrawlUrlRequest):
    """📄 SMART SUMMARIZER: Use for regular web crawling with AI-powered content processing and summarization. Returns clean, processed content optimized for reading. Best for general web content, news articles, and when you need intelligent content extraction. Use this as the DEFAULT choice for most crawling tasks."""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        result = await crawler.crawl_and_process(
            request.url, 
            request.max_content_length,
            request.task_type
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "cloudflare_resistant_jina_crawler"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crawl failed: {str(e)}")

@app.post("/crawl_batch", operation_id="crawl_batch")
async def crawl_batch(request: CrawlBatchRequest):
    """Crawl multiple URLs with Cloudflare resistance"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Process URLs in parallel
        tasks = []
        for url in request.urls:
            task = crawler.crawl_and_process(
                url, 
                request.max_content_length,
                request.task_type
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                formatted_results.append({
                    "success": False,
                    "url": request.urls[i],
                    "error": str(result)
                })
            else:
                formatted_results.append({
                    "success": result.success,
                    "url": result.url,
                    "title": result.title,
                    "processed_content": result.processed_content,
                    "processing_time": result.processing_time,
                    "error": result.error,
                    "metadata": result.metadata
                })
        
        response = {
            "batch_size": len(request.urls),
            "successful_crawls": sum(1 for r in formatted_results if r["success"]),
            "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
            "results": formatted_results,
            "crawler_type": "cloudflare_resistant_jina_batch"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch crawl failed: {str(e)}")

@app.post("/health_check", operation_id="health_check")
async def health_check_tool(request: EmptyRequest = None):
    """Health check tool"""
    global crawler
    
    if not crawler:
        return JSONResponse(content={"status": "error", "message": "Crawler not initialized"})
    
    try:
        health = await crawler.health_check()
        return JSONResponse(content={
            "status": "healthy",
            "crawler_health": health,
            "server_type": "cloudflare_resistant_jina_crawler"
        })
    except Exception as e:
        return JSONResponse(content={"status": "error", "error": str(e)})

@app.post("/get_stats", operation_id="get_stats")
async def get_stats(request: EmptyRequest = None):
    """Get crawler statistics"""
    return JSONResponse(content={
        "server_type": "cloudflare_resistant_jina_crawler",
        "features": [
            "Full OpenAPI 3.0 compliance",
            "Pydantic request validation",
            "Compatible with Open WebUI",
            "No authentication required",
            "Cloudflare resistance with TLS client",
            "Async crawling with aiohttp",
            "BeautifulSoup HTML cleaning",
            "Improved Gemini AI content processing",
            "Better Vietnamese news extraction",
            "Batch processing support",
            "Increased content limits (20K chars)",
            "🔥 Full Article Extraction (50K chars)",
            "🔓 Paywall Bypass (8 techniques)"
        ],
        "tools_available": 9,
        "auth_required": False,
        "cloudflare_resistance": {
            "tls_client_bypass": "Available if tls-client installed",
            "user_agent_rotation": "Dynamic User-Agent spoofing",
            "stealth_headers": "Realistic browser headers",
            "fallback_mechanism": "aiohttp fallback if TLS fails"
        },
        "improved_features": {
            "news_extraction_prompt": "Custom prompt for better news extraction",
            "content_limit": "20000 characters (vs 10000)",
            "output_tokens": "8192 tokens (vs 2048)",
            "task_types": ["news_extraction", "html_to_markdown", "summarize", "clean"]
        }
    })

@app.post("/test_crawl", operation_id="test_crawl")
async def test_crawl(request: EmptyRequest = None):
    """Test crawl with a sample URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Test with a simple URL
        test_url = "https://httpbin.org/html"
        result = await crawler.crawl_and_process(test_url, 5000, "news_extraction")
        
        return JSONResponse(content={
            "test_successful": result.success,
            "test_url": test_url,
            "title": result.title,
            "content_length": len(result.processed_content or ""),
            "processing_time": result.processing_time,
            "error": result.error,
            "cloudflare_bypass_used": result.metadata.get("cloudflare_bypass_used", False) if result.metadata else False,
            "message": "Test crawl completed successfully" if result.success else "Test crawl failed"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "test_successful": False,
            "error": str(e),
            "message": "Test crawl failed with exception"
        })

@app.post("/extract_news", operation_id="extract_news")
async def extract_news(request: CrawlUrlRequest):
    """Extract news with improved prompt (convenience endpoint)"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Force news_extraction task type
        result = await crawler.crawl_and_process(
            request.url, 
            request.max_content_length,
            "news_extraction"
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "cloudflare_resistant_jina_crawler_news"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"News extraction failed: {str(e)}")

@app.post("/crawl_with_fallback", operation_id="crawl_with_fallback")
async def crawl_with_fallback(request: CrawlUrlRequest):
    """🌐 GENERAL PURPOSE CRAWLER: Use for regular websites and public content. Has multiple fallback methods for better success rate but CANNOT bypass paywalls. Use this for normal web crawling when standard crawl_url fails."""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Try multiple methods with different configurations
        methods = [
            ("tls_bypass_news", lambda: crawler.crawl_and_process(request.url, request.max_content_length, "news_extraction")),
            ("tls_bypass_markdown", lambda: crawler.crawl_and_process(request.url, request.max_content_length, "html_to_markdown")),
            ("standard_news", lambda: crawler.crawl_and_process(request.url, 10000, "news_extraction")),
        ]
        
        for method_name, method_func in methods:
            try:
                result = await method_func()
                if result.success:
                    response = {
                        "success": True,
                        "url": result.url,
                        "title": result.title,
                        "processed_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata,
                        "method_used": method_name,
                        "crawler_type": "cloudflare_resistant_jina_crawler_fallback"
                    }
                    return JSONResponse(content=response)
            except Exception as e:
                logger.warning(f"Method {method_name} failed: {e}")
                continue
        
        # All methods failed
        return JSONResponse(content={
            "success": False,
            "url": request.url,
            "error": "All crawling methods failed",
            "method_used": "none"
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Fallback crawl failed: {str(e)}")

@app.post("/crawl_full_article", operation_id="crawl_full_article")
async def crawl_full_article(request: CrawlUrlRequest):
    """📰 COMPLETE ARTICLE EXTRACTOR: Use when you need the ENTIRE article text without any summarization or processing. Returns raw, complete content from public articles. Perfect for when user asks for 'full article', 'complete content', or 'entire text'. Does NOT bypass paywalls."""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Use the full article method from the regular crawler
        result = await crawler.crawl_full_article(
            request.url,
            request.max_content_length or 50000
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "full_article_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "cloudflare_resistant_full_article",
            "content_type": "complete_article"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Full article crawl failed: {str(e)}")

@app.post("/crawl_bypass_paywall", operation_id="crawl_bypass_paywall")
async def crawl_bypass_paywall(request: CrawlUrlRequest):
    """🔓 PAYWALL BYPASS SPECIALIST: Use ONLY for Medium, NYTimes, WSJ, Bloomberg, and other subscription/paywall-protected articles. Uses 8 specialized bypass techniques (Google Cache, Archive.org, 12ft.io, etc.). DO NOT use for regular public websites."""
    global paywall_crawler
    
    if not paywall_crawler:
        raise HTTPException(status_code=500, detail="Paywall crawler not initialized")
    
    try:
        result = await paywall_crawler.crawl_with_paywall_bypass(
            request.url,
            request.max_content_length or 50000
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "full_article_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "paywall_bypass",
            "bypass_method": result.metadata.get("bypass_method", "unknown") if result.metadata else "unknown",
            "content_type": "paywall_bypassed_article"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Paywall bypass failed: {str(e)}")

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8009))
    uvicorn.run(
        "cloudflare_resistant_http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )