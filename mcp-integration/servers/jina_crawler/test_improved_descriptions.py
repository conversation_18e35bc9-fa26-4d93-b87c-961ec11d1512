#!/usr/bin/env python3
"""
Test script to verify improved tool descriptions
"""

import requests
import json

def test_openapi_descriptions():
    """Test that OpenAPI schema has improved descriptions"""
    
    print("🔍 Testing improved tool descriptions...")
    
    try:
        # Test the HTTP server
        response = requests.get("http://localhost:8009/openapi.json")
        if response.status_code == 200:
            openapi = response.json()
            paths = openapi.get('paths', {})
            
            print(f"✅ Found {len(paths)} endpoints")
            
            # Check specific tools
            tools_to_check = [
                '/crawl_bypass_paywall',
                '/crawl_full_article', 
                '/crawl_with_fallback',
                '/crawl_url'
            ]
            
            for tool_path in tools_to_check:
                if tool_path in paths:
                    tool_info = paths[tool_path].get('post', {})
                    description = tool_info.get('description', 'No description')
                    print(f"\n🔧 {tool_path}:")
                    print(f"   Description: {description}")
                    
                    # Check for key differentiating words
                    if tool_path == '/crawl_bypass_paywall':
                        if 'PAYWALL BYPASS' in description and 'Medium' in description:
                            print("   ✅ Has clear paywall bypass indicators")
                        else:
                            print("   ❌ Missing paywall bypass indicators")
                    
                    elif tool_path == '/crawl_full_article':
                        if 'COMPLETE ARTICLE' in description and 'entire' in description:
                            print("   ✅ Has clear full article indicators")
                        else:
                            print("   ❌ Missing full article indicators")
                    
                    elif tool_path == '/crawl_with_fallback':
                        if 'GENERAL PURPOSE' in description and 'CANNOT bypass paywalls' in description:
                            print("   ✅ Has clear general purpose indicators")
                        else:
                            print("   ❌ Missing general purpose indicators")
                            
                    elif tool_path == '/crawl_url':
                        if 'SMART SUMMARIZER' in description and 'DEFAULT choice' in description:
                            print("   ✅ Has clear default choice indicators")
                        else:
                            print("   ❌ Missing default choice indicators")
                else:
                    print(f"❌ {tool_path} not found in OpenAPI spec")
            
            print(f"\n📋 Summary:")
            print(f"- Total endpoints: {len(paths)}")
            print(f"- Key tools checked: {len([t for t in tools_to_check if t in paths])}/{len(tools_to_check)}")
            
        else:
            print(f"❌ Failed to get OpenAPI spec: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing descriptions: {e}")

def test_tool_selection_scenarios():
    """Test scenarios that should trigger specific tools"""
    
    print(f"\n🎯 Testing tool selection scenarios:")
    
    scenarios = [
        {
            "user_request": "Crawl this Medium article behind paywall",
            "expected_tool": "crawl_bypass_paywall",
            "keywords": ["Medium", "paywall", "bypass"]
        },
        {
            "user_request": "Get the full article content without summarization", 
            "expected_tool": "crawl_full_article",
            "keywords": ["full article", "complete", "without summarization"]
        },
        {
            "user_request": "Crawl this regular website",
            "expected_tool": "crawl_url", 
            "keywords": ["regular", "default", "summarizer"]
        },
        {
            "user_request": "Try multiple methods to crawl this difficult site",
            "expected_tool": "crawl_with_fallback",
            "keywords": ["multiple methods", "fallback", "general purpose"]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📝 Scenario: '{scenario['user_request']}'")
        print(f"   Expected tool: {scenario['expected_tool']}")
        print(f"   Key indicators: {', '.join(scenario['keywords'])}")

if __name__ == "__main__":
    print("🚀 Testing Improved Tool Descriptions")
    print("=" * 50)
    
    test_openapi_descriptions()
    test_tool_selection_scenarios()
    
    print(f"\n✅ Test completed!")
    print(f"\n🔄 Next steps:")
    print(f"1. Restart your MCPO server on port 8009")
    print(f"2. Restart Open WebUI container") 
    print(f"3. Clear browser cache")
    print(f"4. Test with specific prompts:")
    print(f"   - 'Use paywall bypass for this Medium article'")
    print(f"   - 'Get the complete article content'")
    print(f"   - 'Crawl this regular website'")