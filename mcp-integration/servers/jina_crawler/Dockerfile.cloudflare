FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install FastAPI, uvicorn, pydantic, and tls-client
RUN pip install fastapi uvicorn pydantic tls-client fake-useragent

# Copy all files
COPY . .

# Expose port
EXPOSE 8009

# Run Cloudflare resistant HTTP wrapper
CMD ["python", "cloudflare_resistant_http_wrapper.py"]
