FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install FastAPI and uvicorn
RUN pip install fastapi uvicorn

# Copy all files
COPY . .

# Expose port
EXPOSE 8002

# Run standard OpenAPI wrapper
CMD ["python", "standard_openapi_wrapper.py"]
