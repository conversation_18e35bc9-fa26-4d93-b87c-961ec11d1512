#!/usr/bin/env python3
"""
Test script for the new crawl_full_article functionality
"""

import asyncio
import json
from jini_crawler import Jin<PERSON><PERSON><PERSON><PERSON>

async def test_full_article_feature():
    """Test the new full article crawling feature"""
    print("🧪 Testing Full Article Crawling Feature")
    print("=" * 60)
    
    crawler = JiniCrawler()
    await crawler.initialize()
    
    # Test URLs - Medium article as requested
    test_urls = [
        "https://medium.com/@ignacio.de.gregorio.noblejas/what-china-gets-the-us-doesnt-f12059d0613d"
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 Test {i}: {url}")
        print("-" * 40)
        
        try:
            # Test regular crawl (summarized)
            print("📝 Regular crawl (summarized):")
            regular_result = await crawler.crawl_and_process(url, max_content_length=10000)
            
            if regular_result.success:
                print(f"   ✅ Success: {regular_result.title}")
                print(f"   📊 Content length: {len(regular_result.processed_content or '')} chars")
                print(f"   ⏱️  Processing time: {regular_result.processing_time:.2f}s")
                if regular_result.processed_content:
                    preview = regular_result.processed_content[:150] + "..." if len(regular_result.processed_content) > 150 else regular_result.processed_content
                    print(f"   📄 Preview: {preview}")
            else:
                print(f"   ❌ Failed: {regular_result.error}")
            
            # Test full article crawl
            print("\n📰 Full article crawl:")
            full_result = await crawler.crawl_full_article(url, max_content_length=50000)
            
            if full_result.success:
                print(f"   ✅ Success: {full_result.title}")
                print(f"   📊 Full content length: {len(full_result.processed_content or '')} chars")
                print(f"   ⏱️  Processing time: {full_result.processing_time:.2f}s")
                print(f"   🏷️  Content type: {full_result.metadata.get('content_type', 'unknown')}")
                if full_result.processed_content:
                    preview = full_result.processed_content[:200] + "..." if len(full_result.processed_content) > 200 else full_result.processed_content
                    print(f"   📄 Preview: {preview}")
                
                # Compare lengths
                regular_len = len(regular_result.processed_content or '')
                full_len = len(full_result.processed_content or '')
                if full_len > regular_len:
                    print(f"   📈 Full article is {full_len - regular_len} chars longer ({((full_len/regular_len - 1) * 100):.1f}% more content)")
                else:
                    print(f"   📉 Full article is similar length to regular crawl")
            else:
                print(f"   ❌ Failed: {full_result.error}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")
        
        print()
    
    # Health check
    print("🏥 Health Check:")
    health = await crawler.health_check()
    print(f"   Status: {health.get('status', 'unknown')}")
    print(f"   Gemini: {health.get('gemini_processor', {}).get('status', 'unknown')}")
    
    await crawler.cleanup()
    print("\n✅ Full Article Feature Test Completed!")

if __name__ == "__main__":
    asyncio.run(test_full_article_feature())