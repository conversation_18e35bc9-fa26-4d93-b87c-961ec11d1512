FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for Playwright
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install --with-deps chromium firefox webkit

# Copy all server files
COPY servers/ ./servers/
COPY config/ ./config/

# Expose port
EXPOSE 5000

# Start MCPO server
CMD ["mcpo", "--host", "0.0.0.0", "--port", "5000", "--api-key", "acca-enhanced-rag-mcp-key-2025", "--config", "config/mcpo_config_docker.json", "--cors-allow-origins", "*"]